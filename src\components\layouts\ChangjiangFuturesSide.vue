<script lang="ts" setup>
import {
  Document,
  Menu as IconMenu,
  Location,
  Setting,
} from '@element-plus/icons-vue'

function handleOpen(key: string, keyPath: string[]) {
  console.log(key, keyPath)
}
function handleClose(key: string, keyPath: string[]) {
  console.log(key, keyPath)
}
</script>

<template>
  <el-menu
    router
    default-active="1"
    class="el-menu-vertical-demo"
    @open="handleOpen"
    @close="handleClose"
  >
    <!-- 风险管理驾驶舱 -->
    <el-menu-item index="/changjiang-futures/risk-dashboard">
      <el-icon>
        <IconMenu />
      </el-icon>
      <template #title>
        风险管理驾驶舱
      </template>
    </el-menu-item>

    <!-- 业务风险管理 -->
    <el-sub-menu index="business-risk">
      <template #title>
        <el-icon>
          <Document />
        </el-icon>
        <span>业务风险管理</span>
      </template>
      
      <!-- 资产管理业务 -->
      <el-sub-menu index="asset-management">
        <template #title>
          <span>资产管理业务</span>
        </template>
        <el-menu-item index="/changjiang-futures/product-distribution">
          产品分布表
        </el-menu-item>
        <el-menu-item index="/changjiang-futures/asset-penetration">
          资管产品穿透持仓表
        </el-menu-item>
      </el-sub-menu>
    </el-sub-menu>

    <!-- 长江产业风险管理 -->
    <el-sub-menu index="industry-risk">
      <template #title>
        <el-icon>
          <Setting />
        </el-icon>
        <span>长江产业风险管理</span>
      </template>
      
      <!-- 做市业务 -->
      <el-sub-menu index="market-making">
        <template #title>
          <span>做市业务</span>
        </template>
        <el-menu-item index="/changjiang-futures/market-making-positions">
          做市业务持仓表
        </el-menu-item>
      </el-sub-menu>

      <!-- 期现业务 -->
      <el-sub-menu index="spot-futures">
        <template #title>
          <span>期现业务</span>
        </template>
        <el-menu-item index="/changjiang-futures/spot-futures-positions">
          期现业务持仓表
        </el-menu-item>
      </el-sub-menu>

      <!-- 场外业务 -->
      <el-sub-menu index="otc-business">
        <template #title>
          <span>场外业务</span>
        </template>
        <el-menu-item index="/changjiang-futures/otc-positions">
          场外业务持仓表
        </el-menu-item>
      </el-sub-menu>
    </el-sub-menu>
  </el-menu>
</template>

<style scoped>
.el-menu-vertical-demo {
  width: 200px;
  min-height: 400px;
}
</style>
