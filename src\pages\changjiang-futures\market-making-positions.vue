<template>
  <div class="market-making-positions">
    <h2>做市业务持仓表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="filterForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="开始日期">
              <el-date-picker
                v-model="filterForm.startDate"
                type="date"
                placeholder="选择开始日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束日期">
              <el-date-picker
                v-model="filterForm.endDate"
                type="date"
                placeholder="选择结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="交易账户">
              <el-input v-model="filterForm.tradingAccount" placeholder="请输入交易账户" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="客户账号">
              <el-input v-model="filterForm.clientAccount" placeholder="请输入客户账号" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户名称">
              <el-input v-model="filterForm.clientName" placeholder="请输入客户名称" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="品种">
              <el-input v-model="filterForm.variety" placeholder="请输入品种" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="合约">
              <el-input v-model="filterForm.contract" placeholder="请输入合约" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="paginatedData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="date" label="日期" width="100" align="center" />
        <el-table-column prop="tradingAccount" label="交易账户" width="120" align="center" />
        <el-table-column prop="clientAccount" label="客户账号" width="120" align="center" />
        <el-table-column prop="clientName" label="客户名称" width="300" />
        <el-table-column prop="variety" label="品种" width="80" align="center" />
        <el-table-column prop="contract" label="合约" width="120" align="center" />
        <el-table-column prop="buyPositions" label="买持仓手数" width="120" align="right" />
        <el-table-column prop="sellPositions" label="卖持仓手数" width="120" align="right" />
        <el-table-column prop="capitalOccupied" label="资金占用规模（元）" width="150" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.capitalOccupied) }}
          </template>
        </el-table-column>
        <el-table-column prop="notionalPrincipal" label="名义本金（元）" width="150" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.notionalPrincipal) }}
          </template>
        </el-table-column>
        <el-table-column prop="deltaExposure" label="delta敞口（元）" width="130" align="right">
          <template #default="scope">
            <span :class="getExposureClass(scope.row.deltaExposure)">
              {{ formatNumber(scope.row.deltaExposure) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="gammaExposure" label="gamma敞口（元）" width="130" align="right">
          <template #default="scope">
            <span :class="getExposureClass(scope.row.gammaExposure)">
              {{ formatNumber(scope.row.gammaExposure) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="vegaExposure" label="vega敞口" width="120" align="right">
          <template #default="scope">
            <span :class="getExposureClass(scope.row.vegaExposure)">
              {{ scope.row.vegaExposure }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

const loading = ref(false)

const filterForm = reactive({
  startDate: '',
  endDate: '',
  tradingAccount: '',
  clientAccount: '',
  clientName: '',
  variety: '',
  contract: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 20
})

// 生成20条样例数据
const marketMakingData = ref([
  {
    date: '2025-06-18',
    tradingAccount: '800389',
    clientAccount: '800389',
    clientName: '长江产业金融服务（武汉）有限公司',
    variety: 'AG',
    contract: 'AG2510',
    buyPositions: 1800,
    sellPositions: 1800,
    capitalOccupied: ********,
    notionalPrincipal: *********,
    deltaExposure: 0,
    gammaExposure: 0,
    vegaExposure: 0
  },
  {
    date: '2025-06-18',
    tradingAccount: '800389',
    clientAccount: '800389',
    clientName: '长江产业金融服务（武汉）有限公司',
    variety: 'BR',
    contract: 'BR2508',
    buyPositions: 2510,
    sellPositions: 2510,
    capitalOccupied: ********.5,
    notionalPrincipal: *********,
    deltaExposure: 0,
    gammaExposure: 0,
    vegaExposure: 0
  }
])

// 计算属性 - 过滤数据
const filteredData = computed(() => {
  let filtered = marketMakingData.value

  // 按日期筛选
  if (filterForm.startDate) {
    filtered = filtered.filter(item => item.date >= filterForm.startDate)
  }
  if (filterForm.endDate) {
    filtered = filtered.filter(item => item.date <= filterForm.endDate)
  }

  // 按其他条件筛选
  if (filterForm.tradingAccount) {
    filtered = filtered.filter(item => item.tradingAccount.includes(filterForm.tradingAccount))
  }
  if (filterForm.clientAccount) {
    filtered = filtered.filter(item => item.clientAccount.includes(filterForm.clientAccount))
  }
  if (filterForm.clientName) {
    filtered = filtered.filter(item => item.clientName.includes(filterForm.clientName))
  }
  if (filterForm.variety) {
    filtered = filtered.filter(item => item.variety.includes(filterForm.variety))
  }
  if (filterForm.contract) {
    filtered = filtered.filter(item => item.contract.includes(filterForm.contract))
  }

  return filtered
})

// 分页数据
const paginatedData = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredData.value.slice(start, end)
})

// 格式化数字（不带货币符号）
const formatNumber = (value: number) => {
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value)
}

// 获取敞口样式
const getExposureClass = (exposure: number) => {
  if (exposure > 0) return 'positive-exposure'
  if (exposure < 0) return 'negative-exposure'
  return 'zero-exposure'
}

const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询完成')
    // 更新分页总数
    pagination.total = filteredData.value.length
  }, 1000)
}

const handleReset = () => {
  Object.assign(filterForm, {
    startDate: '',
    endDate: '',
    tradingAccount: '',
    clientAccount: '',
    clientName: '',
    variety: '',
    contract: ''
  })
  handleSearch()
}

const handleExport = () => {
  ElMessage.success('导出成功')
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  handleSearch()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  handleSearch()
}
</script>

<style scoped>
.market-making-positions {
  padding: 20px;
}

.query-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.query-form {
  margin: 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.positive-exposure {
  color: #67c23a;
  font-weight: 600;
}

.negative-exposure {
  color: #f56c6c;
  font-weight: 600;
}

.zero-exposure {
  color: #909399;
  font-weight: 600;
}
</style>
