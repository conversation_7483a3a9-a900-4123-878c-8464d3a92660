<template>
  <div class="market-making-positions-container">
      <el-card class="main-card">
        <template #header>
          <div class="card-header">
            <span>做市业务持仓表</span>
            <div class="header-actions">
              <el-button type="primary" size="small">导出Excel</el-button>
              <el-button type="success" size="small">刷新数据</el-button>
            </div>
          </div>
        </template>

        <!-- 筛选条件 -->
        <div class="filter-section">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-select v-model="filterForm.variety" placeholder="品种" clearable>
                <el-option label="螺纹钢" value="RB" />
                <el-option label="铜" value="CU" />
                <el-option label="原油" value="SC" />
                <el-option label="铁矿石" value="I" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select v-model="filterForm.direction" placeholder="方向" clearable>
                <el-option label="多头" value="long" />
                <el-option label="空头" value="short" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-date-picker
                v-model="filterForm.tradeDate"
                type="date"
                placeholder="交易日期"
                size="default"
              />
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 汇总信息 -->
        <div class="summary-section">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="总持仓量" :value="summaryData.totalPosition" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="总市值(万元)" :value="summaryData.totalMarketValue" :precision="2" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="总盈亏(万元)" :value="summaryData.totalProfitLoss" :precision="2" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="持仓品种数" :value="summaryData.varietyCount" />
            </el-col>
          </el-row>
        </div>

        <!-- 数据表格 -->
        <el-table :data="positionData" style="width: 100%" v-loading="loading">
          <el-table-column prop="variety" label="品种" width="80" />
          <el-table-column prop="contract" label="合约" width="100" />
          <el-table-column prop="direction" label="方向" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.direction === '多头' ? 'success' : 'danger'">
                {{ scope.row.direction }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="position" label="持仓量" width="100" />
          <el-table-column prop="avgPrice" label="均价" width="100" />
          <el-table-column prop="currentPrice" label="现价" width="100" />
          <el-table-column prop="marketValue" label="市值(万元)" width="120" />
          <el-table-column prop="profitLoss" label="浮动盈亏(万元)" width="140">
            <template #default="scope">
              <span :class="scope.row.profitLoss >= 0 ? 'profit' : 'loss'">
                {{ scope.row.profitLoss }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="margin" label="保证金(万元)" width="120" />
          <el-table-column prop="riskDegree" label="风险度(%)" width="100">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.riskDegree"
                :color="getRiskColor(scope.row.riskDegree)"
                :show-text="false"
              />
              <span class="risk-text">{{ scope.row.riskDegree }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="150" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const loading = ref(false)

const filterForm = reactive({
  variety: '',
  direction: '',
  tradeDate: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 80
})

const summaryData = reactive({
  totalPosition: 1250,
  totalMarketValue: 15678.90,
  totalProfitLoss: 234.56,
  varietyCount: 8
})

const positionData = ref([
  {
    variety: 'RB',
    contract: 'RB2405',
    direction: '多头',
    position: 100,
    avgPrice: 4000.00,
    currentPrice: 4050.00,
    marketValue: 4050.00,
    profitLoss: 50.00,
    margin: 400.00,
    riskDegree: 15.6,
    updateTime: '2024-01-15 14:30:00'
  },
  {
    variety: 'CU',
    contract: 'CU2405',
    direction: '空头',
    position: 50,
    avgPrice: 70000.00,
    currentPrice: 69500.00,
    marketValue: 3475.00,
    profitLoss: 25.00,
    margin: 350.00,
    riskDegree: 12.3,
    updateTime: '2024-01-15 14:25:00'
  },
  {
    variety: 'SC',
    contract: 'SC2405',
    direction: '多头',
    position: 20,
    avgPrice: 600.00,
    currentPrice: 590.00,
    marketValue: 1180.00,
    profitLoss: -20.00,
    margin: 120.00,
    riskDegree: 25.8,
    updateTime: '2024-01-15 14:20:00'
  },
  {
    variety: 'I',
    contract: 'I2405',
    direction: '多头',
    position: 80,
    avgPrice: 800.00,
    currentPrice: 820.00,
    marketValue: 6560.00,
    profitLoss: 160.00,
    margin: 656.00,
    riskDegree: 8.9,
    updateTime: '2024-01-15 14:15:00'
  }
])

const getRiskColor = (riskDegree: number) => {
  if (riskDegree < 10) return '#67c23a'
  if (riskDegree < 20) return '#e6a23c'
  return '#f56c6c'
}

const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询完成')
  }, 1000)
}

const handleReset = () => {
  Object.assign(filterForm, {
    variety: '',
    direction: '',
    tradeDate: ''
  })
}

const handleDetail = (row: any) => {
  ElMessage.info(`查看详情: ${row.contract}`)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  handleSearch()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  handleSearch()
}
</script>

<style scoped>
.market-making-positions-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.main-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.summary-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.profit {
  color: #67c23a;
  font-weight: bold;
}

.loss {
  color: #f56c6c;
  font-weight: bold;
}

.risk-text {
  margin-left: 8px;
  font-size: 12px;
}
</style>
