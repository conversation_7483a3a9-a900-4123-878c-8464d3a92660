<template>
  <div class="report-entry-container">
    <!-- 查询区域 -->
    <el-form :inline="true" :model="searchForm" label-width="80px" class="search-form">
      <el-form-item label="报表名称">
        <el-input v-model="searchForm.reportName" placeholder="请输入报表名称" />
      </el-form-item>

      <el-form-item label="日期范围">
        <el-date-picker
          v-model="searchForm.dateRange"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button type="success" icon="DocumentAdd">数据录入</el-button>
      <el-button type="warning" icon="Download">模板下载</el-button>
    </div>

    <!-- 表格 -->
    <el-table :data="paginatedData" border style="width: 100%" v-loading="loading">
      <el-table-column prop="id" label="ID" width="60" align="center" />
      <el-table-column prop="reportName" label="报表名称" />
      <el-table-column prop="submitDate" label="提交时间" width="160" align="center" />
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === '已提交' ? 'success' : 'info'">{{ row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" show-overflow-tooltip />
      <el-table-column label="操作" width="150" align="center">
        <template #default="{ $index }">
          <el-button size="small" @click="handleEdit($index)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete($index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      layout="prev, pager, next"
      :total="tableData.length"
      :page-size="pageSize"
      :current-page="currentPage"
      @current-change="handlePageChange"
      style="margin-top: 20px; text-align: center"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// 查询条件
const searchForm = ref({
  reportName: '',
  dateRange: []
})

// 表格数据模拟
const tableData = ref([
  { id: 1, reportName: '库存日报', submitDate: '2024-04-01', status: '已提交', remark: '第一季度汇总' },
  { id: 2, reportName: '销售报表', submitDate: '2024-04-02', status: '草稿', remark: '未完成' },
  { id: 3, reportName: '采购报表', submitDate: '2024-04-03', status: '已提交', remark: '' },
  { id: 4, reportName: '库存日报', submitDate: '2024-04-04', status: '草稿', remark: '测试数据' },
  { id: 5, reportName: '销售报表', submitDate: '2024-04-05', status: '已提交', remark: '完整版' },
  { id: 6, reportName: '采购报表', submitDate: '2024-04-06', status: '草稿', remark: '' },
  { id: 7, reportName: '库存日报', submitDate: '2024-04-07', status: '已提交', remark: '最新' },
  { id: 8, reportName: '销售报表', submitDate: '2024-04-08', status: '草稿', remark: '待补充' }
])

// 分页配置
const pageSize = 6
const currentPage = ref(1)
const loading = ref(false)

// 分页计算
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  return tableData.value.slice(start, start + pageSize)
})

// 分页事件
const handlePageChange = (page) => {
  currentPage.value = page
}

// 查询
const onSearch = () => {
  console.log('执行搜索:', searchForm.value)
}

// 重置
const onReset = () => {
  searchForm.value = {
    reportName: '',
    dateRange: []
  }
}

// 编辑
const handleEdit = (index) => {
  alert(`编辑第 ${index + 1} 行`)
}

// 删除
const handleDelete = (index) => {
  tableData.value.splice(index, 1)
}
</script>

<style scoped lang="scss">
.report-entry-container {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.action-buttons {
  margin-bottom: 20px;
}
</style>