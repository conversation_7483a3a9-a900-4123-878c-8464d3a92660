<script lang="ts" setup>
import {
  Document,
  Menu as IconMenu,
  Location,
  Setting,
} from '@element-plus/icons-vue'

function handleOpen(key: string, keyPath: string[]) {
  console.log(key, keyPath)
}
function handleClose(key: string, keyPath: string[]) {
  console.log(key, keyPath)
}
</script>

<template>
  <el-menu
    router
    default-active="1"
    class="el-menu-vertical-demo"
    @open="handleOpen"
    @close="handleClose"
  >
    <!-- 长江产业驾驶舱 -->
    <el-menu-item index="/changjiang-industry/dashboard">
      <el-icon>
        <IconMenu />
      </el-icon>
      <template #title>
        长江产业驾驶舱
      </template>
    </el-menu-item>
  </el-menu>
</template>

<style scoped>
.el-menu-vertical-demo {
  width: 200px;
  min-height: 400px;
}
</style>
