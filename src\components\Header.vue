<template>
  <el-tabs v-model="activeTab" @tab-click="handleTabClick" style="margin-bottom: 10px;">
    <el-tab-pane
      v-for="tab in tabs"
      :key="tab.key"
      :label="tab.name"
      :name="tab.key"
    />
  </el-tabs>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue';

const props = defineProps({
  tabs: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits(['tab-change']);

const activeTab = ref(props.tabs[0].key);

const handleTabClick = (tab) => {
  emit('tab-change', tab.name);
};
</script>

<style scoped>
/*  可以自定义 Tab 样式 */
</style>