<template>
  <div class="product-distribution-container">
      <el-card class="main-card">
        <template #header>
          <div class="card-header">
            <span>产品分布表</span>
            <div class="header-actions">
              <el-button type="primary" size="small">导出Excel</el-button>
              <el-button type="success" size="small">刷新数据</el-button>
            </div>
          </div>
        </template>

        <!-- 筛选条件 -->
        <div class="filter-section">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-select v-model="filterForm.productType" placeholder="产品类型" clearable>
                <el-option label="股票型" value="stock" />
                <el-option label="债券型" value="bond" />
                <el-option label="混合型" value="mixed" />
                <el-option label="货币型" value="money" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select v-model="filterForm.riskLevel" placeholder="风险等级" clearable>
                <el-option label="低风险" value="low" />
                <el-option label="中风险" value="medium" />
                <el-option label="高风险" value="high" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-date-picker
                v-model="filterForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="default"
              />
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 数据表格 -->
        <el-table :data="productData" style="width: 100%" v-loading="loading">
          <el-table-column prop="productCode" label="产品代码" width="120" />
          <el-table-column prop="productName" label="产品名称" width="200" />
          <el-table-column prop="productType" label="产品类型" width="100">
            <template #default="scope">
              <el-tag :type="getProductTypeTag(scope.row.productType)">
                {{ getProductTypeName(scope.row.productType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="riskLevel" label="风险等级" width="100">
            <template #default="scope">
              <el-tag :type="getRiskLevelTag(scope.row.riskLevel)">
                {{ scope.row.riskLevel }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="netValue" label="净值" width="120" />
          <el-table-column prop="scale" label="规模(万元)" width="120" />
          <el-table-column prop="manager" label="基金经理" width="120" />
          <el-table-column prop="establishDate" label="成立日期" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === '正常' ? 'success' : 'danger'">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleView(scope.row)">查看</el-button>
              <el-button type="warning" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const loading = ref(false)

const filterForm = reactive({
  productType: '',
  riskLevel: '',
  dateRange: []
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 100
})

const productData = ref([
  {
    productCode: 'CJ001',
    productName: '长江稳健增长1号',
    productType: 'mixed',
    riskLevel: '中风险',
    netValue: '1.2345',
    scale: '50000',
    manager: '张三',
    establishDate: '2023-01-15',
    status: '正常'
  },
  {
    productCode: 'CJ002',
    productName: '长江价值精选2号',
    productType: 'stock',
    riskLevel: '高风险',
    netValue: '1.5678',
    scale: '80000',
    manager: '李四',
    establishDate: '2023-03-20',
    status: '正常'
  },
  {
    productCode: 'CJ003',
    productName: '长江债券优选3号',
    productType: 'bond',
    riskLevel: '低风险',
    netValue: '1.0234',
    scale: '30000',
    manager: '王五',
    establishDate: '2023-05-10',
    status: '暂停'
  }
])

const getProductTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    stock: 'danger',
    bond: 'success',
    mixed: 'warning',
    money: 'info'
  }
  return tagMap[type] || ''
}

const getProductTypeName = (type: string) => {
  const nameMap: Record<string, string> = {
    stock: '股票型',
    bond: '债券型',
    mixed: '混合型',
    money: '货币型'
  }
  return nameMap[type] || type
}

const getRiskLevelTag = (level: string) => {
  const tagMap: Record<string, string> = {
    '低风险': 'success',
    '中风险': 'warning',
    '高风险': 'danger'
  }
  return tagMap[level] || ''
}

const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询完成')
  }, 1000)
}

const handleReset = () => {
  Object.assign(filterForm, {
    productType: '',
    riskLevel: '',
    dateRange: []
  })
}

const handleView = (row: any) => {
  ElMessage.info(`查看产品: ${row.productName}`)
}

const handleEdit = (row: any) => {
  ElMessage.info(`编辑产品: ${row.productName}`)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  handleSearch()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  handleSearch()
}
</script>

<style scoped>
.product-distribution-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.main-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
