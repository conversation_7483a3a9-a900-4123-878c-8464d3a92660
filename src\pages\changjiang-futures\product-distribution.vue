<template>
  <div class="product-distribution">
    <h2>产品分布表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="filterForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="开始日期">
              <el-date-picker
                v-model="filterForm.startDate"
                type="date"
                placeholder="选择开始日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束日期">
              <el-date-picker
                v-model="filterForm.endDate"
                type="date"
                placeholder="选择结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="类别">
              <el-select v-model="filterForm.category" placeholder="请选择类别" clearable style="width: 100%">
                <el-option label="所有" value="all" />
                <el-option label="产品类型" value="product-type" />
                <el-option label="投资类型" value="investment-type" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="产品分类">
              <el-select v-model="filterForm.productClassification" placeholder="请选择产品分类" clearable style="width: 100%">
                <el-option label="所有" value="all" />
                <el-option label="期货和衍生品类" value="futures-derivatives" />
                <el-option label="单一" value="single" />
                <el-option label="集合" value="collective" />
                <el-option label="混合类" value="mixed" />
                <el-option label="固定收益类" value="fixed-income" />
                <el-option label="商品及金融衍生品类" value="commodity-financial-derivatives" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="paginatedData" 
        ref="table"
        border
        stripe
        v-loading="loading"
        style="width: 100%"
        table-layout="auto">
        <el-table-column prop="date" label="日期" width="120" align="center" />
        <el-table-column prop="category" label="类别" width="120" align="center" />
        <el-table-column prop="productClassification" label="产品分类" width="180" align="center" />
        <el-table-column prop="productCount" label="产品数量（只）" width="140" align="right" />
        <el-table-column prop="productScale" label="产品规模（元）" width="200" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.productScale) }}
          </template>
        </el-table-column>
        <el-table-column prop="scaleRatio" label="规模占比" width="120" align="right">
          <template #default="scope">
            <span :class="getScaleRatioClass(scope.row.scaleRatio)">
              {{ scope.row.scaleRatio }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

const loading = ref(false)

const filterForm = reactive({
  startDate: '',
  endDate: '',
  category: '',
  productClassification: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 20
})

// 生成20条样例数据
const productData = ref([
  {
    date: '2025-06-18',
    category: '投资类型',
    productClassification: '期货和衍生品类',
    productCount: 4,
    productScale: 37596221.92,
    scaleRatio: '3.05%'
  },
  {
    date: '2025-06-18',
    category: '投资类型',
    productClassification: '混合类',
    productCount: 3,
    productScale: 53305451.81,
    scaleRatio: '4.33%'
  },
  {
    date: '2025-06-18',
    category: '投资类型',
    productClassification: '固定收益类',
    productCount: 19,
    productScale: 1139873663.92,
    scaleRatio: '92.61%'
  },
  {
    date: '2025-06-17',
    category: '产品类型',
    productClassification: '集合',
    productCount: 21,
    productScale: 1024589540.15,
    scaleRatio: '83.27%'
  },
  {
    date: '2025-06-17',
    category: '产品类型',
    productClassification: '单一',
    productCount: 8,
    productScale: 205678432.50,
    scaleRatio: '16.73%'
  },
  {
    date: '2025-06-16',
    category: '投资类型',
    productClassification: '商品及金融衍生品类',
    productCount: 12,
    productScale: 456789123.45,
    scaleRatio: '35.67%'
  },
  {
    date: '2025-06-16',
    category: '投资类型',
    productClassification: '固定收益类',
    productCount: 25,
    productScale: 823456789.12,
    scaleRatio: '64.33%'
  },
  {
    date: '2025-06-15',
    category: '产品类型',
    productClassification: '混合类',
    productCount: 15,
    productScale: 678912345.67,
    scaleRatio: '45.28%'
  },
  {
    date: '2025-06-15',
    category: '产品类型',
    productClassification: '集合',
    productCount: 18,
    productScale: 820134567.89,
    scaleRatio: '54.72%'
  },
  {
    date: '2025-06-14',
    category: '投资类型',
    productClassification: '期货和衍生品类',
    productCount: 6,
    productScale: 123456789.01,
    scaleRatio: '12.35%'
  },
  {
    date: '2025-06-14',
    category: '投资类型',
    productClassification: '固定收益类',
    productCount: 22,
    productScale: 876543210.98,
    scaleRatio: '87.65%'
  },
  {
    date: '2025-06-13',
    category: '产品类型',
    productClassification: '单一',
    productCount: 10,
    productScale: 345678901.23,
    scaleRatio: '28.47%'
  },
  {
    date: '2025-06-13',
    category: '产品类型',
    productClassification: '集合',
    productCount: 16,
    productScale: 867890123.45,
    scaleRatio: '71.53%'
  },
  {
    date: '2025-06-12',
    category: '投资类型',
    productClassification: '商品及金融衍生品类',
    productCount: 9,
    productScale: 234567890.12,
    scaleRatio: '19.56%'
  },
  {
    date: '2025-06-12',
    category: '投资类型',
    productClassification: '混合类',
    productCount: 14,
    productScale: 965432109.87,
    scaleRatio: '80.44%'
  },
  {
    date: '2025-06-11',
    category: '产品类型',
    productClassification: '固定收益类',
    productCount: 28,
    productScale: 1456789012.34,
    scaleRatio: '89.23%'
  },
  {
    date: '2025-06-11',
    category: '产品类型',
    productClassification: '期货和衍生品类',
    productCount: 5,
    productScale: 175890123.45,
    scaleRatio: '10.77%'
  },
  {
    date: '2025-06-10',
    category: '投资类型',
    productClassification: '单一',
    productCount: 11,
    productScale: 567890123.45,
    scaleRatio: '42.18%'
  },
  {
    date: '2025-06-10',
    category: '投资类型',
    productClassification: '集合',
    productCount: 13,
    productScale: 778901234.56,
    scaleRatio: '57.82%'
  },
  {
    date: '2025-06-09',
    category: '产品类型',
    productClassification: '商品及金融衍生品类',
    productCount: 7,
    productScale: 289012345.67,
    scaleRatio: '24.67%'
  }
])

// 计算属性 - 过滤数据
const filteredProductData = computed(() => {
  let filtered = productData.value

  // 按日期筛选
  if (filterForm.startDate) {
    filtered = filtered.filter(item => item.date >= filterForm.startDate)
  }
  if (filterForm.endDate) {
    filtered = filtered.filter(item => item.date <= filterForm.endDate)
  }

  // 按类别筛选
  if (filterForm.category && filterForm.category !== 'all') {
    const categoryMap: Record<string, string> = {
      'product-type': '产品类型',
      'investment-type': '投资类型'
    }
    filtered = filtered.filter(item => item.category === categoryMap[filterForm.category])
  }

  // 按产品分类筛选
  if (filterForm.productClassification && filterForm.productClassification !== 'all') {
    const classificationMap: Record<string, string> = {
      'futures-derivatives': '期货和衍生品类',
      'single': '单一',
      'collective': '集合',
      'mixed': '混合类',
      'fixed-income': '固定收益类',
      'commodity-financial-derivatives': '商品及金融衍生品类'
    }
    filtered = filtered.filter(item => item.productClassification === classificationMap[filterForm.productClassification])
  }

  return filtered
})

// 分页数据
const paginatedData = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredProductData.value.slice(start, end)
})

// 格式化数字（不带货币符号）
const formatNumber = (value: number) => {
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value)
}

// 获取规模占比样式
const getScaleRatioClass = (ratio: string) => {
  const percentage = parseFloat(ratio.replace('%', ''))
  if (percentage >= 80) return 'high-ratio'
  if (percentage >= 50) return 'medium-ratio'
  return 'low-ratio'
}

const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询完成')
    // 更新分页总数
    pagination.total = filteredProductData.value.length
  }, 1000)
}

const handleReset = () => {
  Object.assign(filterForm, {
    startDate: '',
    endDate: '',
    category: '',
    productClassification: ''
  })
  handleSearch()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  handleSearch()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  handleSearch()
}
</script>

<style scoped>
.product-distribution {
  padding: 20px;
}

.query-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.query-form {
  margin: 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.high-ratio {
  color: #f56c6c;
  font-weight: 600;
}

.medium-ratio {
  color: #e6a23c;
  font-weight: 600;
}

.low-ratio {
  color: #67c23a;
  font-weight: 600;
}
</style>
