<template>
  <div class="dashboard-container">
    <!-- 图表区域 -->
    <div class="inventory-chart">
      <div class="chart-container">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-row :gutter="24">
              <el-col
                v-for="(chart, index) in charts"
                :key="index"
                :span="6"
                class="chart-item"
              >
                <el-card shadow="hover" class="chart-card">
                  <template #header>
                    <div class="card-header">
                      <span>{{ chart.title }}</span>
                      <el-button type="text" @click.stop="openFullScreenChart(chart)">
                        <el-icon><FullScreen /></el-icon>
                      </el-button>
                    </div>
                  </template>
                  <div :id="'chart-' + index" class="echart" style="width: 100%; height: 200px;"></div>
                </el-card>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 全屏图表弹窗 -->
    <el-dialog v-model="isDialogVisible" title="详细图表" width="80%">
      <div id="full-chart" class="full-chart" style="width: 100%; height: 600px;"></div>
    </el-dialog>

    <!-- 研报列表区域 -->
    <el-card class="report-card" shadow="never">
      <div class="filter-bar">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleDateChange"
            />
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="selectedCategory"
              placeholder="报告类型"
              clearable
              @change="handleFilterChange"
            >
              <el-option
                v-for="item in categories"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="selectedStatus"
              placeholder="状态筛选"
              clearable
              @change="handleFilterChange"
            >
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索报告标题/内容"
              clearable
              @input="handleSearch"
            >
              <template #append>
                <el-button :icon="Search" />
              </template>
            </el-input>
          </el-col>
        </el-row>
      </div>

      <!-- 报告列表 -->
      <div class="report-list">
        <template v-if="reportList.length > 0">
          <el-row :gutter="20">
            <el-col
              v-for="report in reportList"
              :key="report.id"
              :xs="24"
              :sm="12"
              :md="8"
              :lg="6"
            >
              <ResearchReportItem
                :report="report"
                @click="handleCardClick(report)"
                @download="handleDownload"
                @favorite="handleFavorite"
              />
            </el-col>
          </el-row>
        </template>
        <el-empty v-else description="暂无报告数据" />
      </div>

      <!-- 分页器 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalReports"
          :page-sizes="[10, 20, 50, 100]"
          layout="prev, pager, next, jumper, total"
          :pager-count="5"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import {
  FullScreen,
  Postcard,
  Notebook,
  Files,
  Search
} from '@element-plus/icons-vue'
import { ref, onMounted, computed } from 'vue'
import * as echarts from 'echarts'
import ResearchReportItem from '~/components/research/ResearchReportItem.vue'

const showCharts = ref(true)
const charts = ref([])

// 获取最近8年
function getLastNYears(n) {
  const years = []
  const currentYear = new Date().getFullYear()
  for (let i = n - 1; i >= 0; i--) {
    years.push(currentYear - i)
  }
  return years
}

const years = getLastNYears(8)

for (let i = 0; i < 4; i++) {
  const seriesData = []

  // 每年一条线，12个月
  years.forEach(year => {
    const data = []
    for (let month = 0; month < 12; month++) {
      const value = Math.floor(Math.random() * 500) + 100
      data.push(value)
    }

    seriesData.push({
      name: `${year}年`,
      type: 'line',
      data: data,
      smooth: true,
      showSymbol: false
    })
  })

  charts.value.push({
    title: `库存-${i + 1}`,
    series: seriesData
  })
}

const loadCharts = () => {
  showCharts.value = true

  setTimeout(() => {
    charts.value.forEach((_, index) => {
      const chartDom = document.getElementById('chart-' + index)
      if (!chartDom) {
        console.error(`未找到图表容器: chart-${index}`)
        return
      }

      const myChart = echarts.init(chartDom)

      // 显示加载动画
      myChart.showLoading({
        text: '加载中...',
        color: '#409EFF',
        textColor: '#666',
        maskColor: 'rgba(255, 255, 255, 0.8)'
      })

      // 构建配置项
      const option = {
        title: {
          text: _.title,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let result = params[0].name + '<br/>'
            params.forEach(p => {
              result += `${p.marker} ${p.seriesName}: ${p.value}<br/>`
            })
            return result
          }
        },
        grid: {
          top: 80,
          bottom: 40,
          left: 60,
          right: 20
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          name: '月份'
        },
        yAxis: {
          type: 'value',
          name: '库存量',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#666'
            }
          }
        },
        legend: {
          data: _.series.map(s => s.name),
          top: 30,
          left: 'center',
          bottom: 10
        },
        series: _.series
      }

      // 设置配置并隐藏 loading
      setTimeout(() => {
        myChart.hideLoading()
        myChart.setOption(option)
      }, 800)
    })
  }, 0)
}

onMounted(() => {
  loadCharts()
})

// 弹窗相关逻辑
const isDialogVisible = ref(false)
let fullChart = null

const openFullScreenChart = (chartData) => {
  isDialogVisible.value = true

  setTimeout(() => {
    if (!fullChart) {
      fullChart = echarts.init(document.getElementById('full-chart'))
    }

    const option = {
      title: {
        text: chartData.title,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          let result = params[0].name + '<br/>'
          params.forEach(p => {
            result += `${p.marker} ${p.seriesName}: ${p.value}<br/>`
          })
          return result
        }
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        name: '月份'
      },
      yAxis: {
        type: 'value',
        name: '库存量',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#666'
          }
        }
      },
      legend: {
        data: chartData.series.map(s => s.name),
        top: 30,
        left: 'center',
        bottom: 10
      },
      series: chartData.series
    }

    fullChart.setOption(option)
  }, 0)
}

// ================== 研报相关数据 ==================
// 筛选条件
const dateRange = ref([])
const selectedCategory = ref('')
const selectedStatus = ref('')
const searchKeyword = ref('')

// 模拟数据选项
const categories = [
  { value: 'macro', label: '宏观报告' },
  { value: 'commodity', label: '商品期货' },
  { value: 'financial', label: '金融期货' },
  { value: 'strategy', label: '投资策略' }
]

const statusOptions = [
  { value: 'draft', label: '草稿' },
  { value: 'published', label: '已发布' },
  { value: 'archived', label: '已归档' }
]

// 测试数据
const mockReports = [
  {
    id: 1,
    title: '2024年第一季度宏观经济分析',
    category: 'macro',
    summary: '本报告对2024年第一季度国内宏观经济运行情况进行全面分析...',
    publishDate: '2024-04-05',
    author: '张三',
    status: 'published',
    downloadCount: 120,
    favoriteCount: 35
  },
  {
    id: 2,
    title: '铜期货市场走势预测报告',
    category: 'commodity',
    summary: '通过对供需关系和技术面的综合分析，预测未来三个月铜价走势...',
    publishDate: '2024-04-04',
    author: '李四',
    status: 'published',
    downloadCount: 89,
    favoriteCount: 27
  },
  {
    id: 3,
    title: '股指期货投资策略建议',
    category: 'financial',
    summary: '基于当前经济形势与政策变化，提出二季度股指期货操作建议...',
    publishDate: '2024-04-03',
    author: '王五',
    status: 'draft',
    downloadCount: 0,
    favoriteCount: 10
  },
  {
    id: 4,
    title: '农产品期货套利机会分析',
    category: 'commodity',
    summary: '分析玉米、大豆等主要农产品之间的跨品种套利机会及风险控制...',
    publishDate: '2024-04-02',
    author: '赵六',
    status: 'published',
    downloadCount: 67,
    favoriteCount: 18
  },
  {
    id: 5,
    title: '2024年中国新能源产业发展趋势展望',
    category: 'macro',
    summary: '本报告总结了中国新能源产业在2024年的最新发展趋势及政策影响...',
    publishDate: '2024-04-01',
    author: '钱七',
    status: 'published',
    downloadCount: 95,
    favoriteCount: 22
  },
  {
    id: 6,
    title: '原油价格波动对全球经济的影响研究',
    category: 'commodity',
    summary: '探讨近期国际原油价格波动对全球主要经济体的影响机制...',
    publishDate: '2024-03-31',
    author: '孙八',
    status: 'published',
    downloadCount: 110,
    favoriteCount: 29
  },
  {
    id: 7,
    title: '人工智能在金融领域的应用前景',
    category: 'financial',
    summary: '分析AI技术在银行、保险、证券等金融细分领域的落地情况及前景...',
    publishDate: '2024-03-30',
    author: '周九',
    status: 'draft',
    downloadCount: 0,
    favoriteCount: 5
  },
  {
    id: 8,
    title: '碳中和目标下的钢铁行业转型分析',
    category: 'macro',
    summary: '研究碳中和政策背景下，钢铁行业的绿色转型路径与挑战...',
    publishDate: '2024-03-29',
    author: '吴十',
    status: 'published',
    downloadCount: 88,
    favoriteCount: 17
  },
  {
    id: 9,
    title: '黄金期货市场短期波动趋势分析',
    category: 'commodity',
    summary: '结合地缘政治与美元走势，分析黄金市场的短期波动趋势...',
    publishDate: '2024-03-28',
    author: '郑十一',
    status: 'published',
    downloadCount: 73,
    favoriteCount: 12
  },
  {
    id: 10,
    title: '期权交易策略与风险管理实践',
    category: 'financial',
    summary: '介绍期权交易中的常见策略及其在实际投资中的风险管理应用...',
    publishDate: '2024-03-27',
    author: '王十二',
    status: 'archived',
    downloadCount: 56,
    favoriteCount: 9
  }
]

const reportListData = ref(mockReports)
const totalReports = ref(reportListData.value.length)
const reportList = ref(reportListData.value.slice(0, 10))

function loadReports() {
  const keyword = searchKeyword.value.toLowerCase()
  const category = selectedCategory.value
  const status = selectedStatus.value

  let filtered = reportListData.value.filter((report) => {
    return (
      (!category || report.category === category) &&
      (!status || report.status === status) &&
      (!keyword ||
        report.title.toLowerCase().includes(keyword) ||
        report.summary.toLowerCase().includes(keyword))
    )
  })

  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value

  reportList.value = filtered.slice(start, end)
  totalReports.value = filtered.length
}

function handleFilterChange() {
  currentPage.value = 1
  loadReports()
}

function handleDateChange() {
  handleFilterChange()
}

function handleSearch() {
  handleFilterChange()
}

function handleSizeChange(val) {
  pageSize.value = val
  loadReports()
}

function handleCurrentChange(val) {
  currentPage.value = val
  loadReports()
}

function handleCardClick(report) {
  console.log('打开报告详情:', report)
}

function handleDownload(report) {
  console.log('下载报告:', report)
}

function handleFavorite(report) {
  console.log('收藏报告:', report)
}
</script>

<style scoped lang="scss">
.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
}

.inventory-chart {
  padding: 20px;
}

.chart-card {
  margin-bottom: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.menu-style {
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #f9f9f9;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.report-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.filter-bar {
  margin-bottom: 20px;
}

.report-list {
  margin-bottom: 20px;
}

.pagination {
  display: flex;
  justify-content: center;
  font-size: 14px;
}

.pagination :deep(.el-pager li) {
  background-color: transparent !important;
  color: #606266;
}

.pagination :deep(.el-pager li.active) {
  color: var(--el-color-primary);
  font-weight: bold;
}
</style>