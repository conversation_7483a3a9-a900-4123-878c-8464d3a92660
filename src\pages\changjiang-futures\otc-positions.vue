<template>
  <div class="otc-positions-container">
      <el-card class="main-card">
        <template #header>
          <div class="card-header">
            <span>场外业务持仓表</span>
            <div class="header-actions">
              <el-button type="primary" size="small">导出Excel</el-button>
              <el-button type="success" size="small">刷新数据</el-button>
            </div>
          </div>
        </template>

        <!-- 筛选条件 -->
        <div class="filter-section">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-select v-model="filterForm.productType" placeholder="产品类型" clearable>
                <el-option label="期权" value="option" />
                <el-option label="互换" value="swap" />
                <el-option label="远期" value="forward" />
                <el-option label="结构化产品" value="structured" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select v-model="filterForm.underlying" placeholder="标的资产" clearable>
                <el-option label="螺纹钢" value="RB" />
                <el-option label="铜" value="CU" />
                <el-option label="原油" value="SC" />
                <el-option label="铁矿石" value="I" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select v-model="filterForm.status" placeholder="状态" clearable>
                <el-option label="存续" value="active" />
                <el-option label="到期" value="expired" />
                <el-option label="提前终止" value="terminated" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 汇总信息 -->
        <div class="summary-section">
          <el-row :gutter="20">
            <el-col :span="4">
              <el-statistic title="总合约数" :value="summaryData.totalContracts" />
            </el-col>
            <el-col :span="4">
              <el-statistic title="名义本金(万元)" :value="summaryData.notionalPrincipal" :precision="2" />
            </el-col>
            <el-col :span="4">
              <el-statistic title="市场价值(万元)" :value="summaryData.marketValue" :precision="2" />
            </el-col>
            <el-col :span="4">
              <el-statistic title="Delta敞口" :value="summaryData.deltaExposure" :precision="2" />
            </el-col>
            <el-col :span="4">
              <el-statistic title="Gamma敞口" :value="summaryData.gammaExposure" :precision="4" />
            </el-col>
            <el-col :span="4">
              <el-statistic title="Vega敞口" :value="summaryData.vegaExposure" :precision="2" />
            </el-col>
          </el-row>
        </div>

        <!-- 数据表格 -->
        <el-table :data="otcData" style="width: 100%" v-loading="loading">
          <el-table-column prop="contractId" label="合约编号" width="120" />
          <el-table-column prop="productType" label="产品类型" width="100">
            <template #default="scope">
              <el-tag :type="getProductTypeTag(scope.row.productType)">
                {{ getProductTypeName(scope.row.productType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="underlying" label="标的资产" width="100" />
          <el-table-column prop="direction" label="方向" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.direction === '买入' ? 'success' : 'danger'">
                {{ scope.row.direction }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="notional" label="名义本金(万元)" width="130" />
          <el-table-column prop="marketValue" label="市场价值(万元)" width="130" />
          <el-table-column prop="delta" label="Delta" width="80" />
          <el-table-column prop="gamma" label="Gamma" width="80" />
          <el-table-column prop="vega" label="Vega" width="80" />
          <el-table-column prop="theta" label="Theta" width="80" />
          <el-table-column prop="profitLoss" label="盈亏(万元)" width="120">
            <template #default="scope">
              <span :class="scope.row.profitLoss >= 0 ? 'profit' : 'loss'">
                {{ scope.row.profitLoss }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="maturityDate" label="到期日" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusTag(scope.row.status)">
                {{ getStatusName(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const loading = ref(false)

const filterForm = reactive({
  productType: '',
  underlying: '',
  status: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 45
})

const summaryData = reactive({
  totalContracts: 45,
  notionalPrincipal: 50000.00,
  marketValue: 2500.00,
  deltaExposure: 150.50,
  gammaExposure: 0.0025,
  vegaExposure: 85.60
})

const otcData = ref([
  {
    contractId: 'OTC001',
    productType: 'option',
    underlying: 'RB',
    direction: '买入',
    notional: 10000.00,
    marketValue: 500.00,
    delta: 0.65,
    gamma: 0.002,
    vega: 15.5,
    theta: -2.3,
    profitLoss: 50.00,
    maturityDate: '2024-06-15',
    status: 'active'
  },
  {
    contractId: 'OTC002',
    productType: 'swap',
    underlying: 'CU',
    direction: '卖出',
    notional: 15000.00,
    marketValue: 750.00,
    delta: -0.45,
    gamma: 0.001,
    vega: 20.8,
    theta: -1.8,
    profitLoss: -25.00,
    maturityDate: '2024-09-20',
    status: 'active'
  },
  {
    contractId: 'OTC003',
    productType: 'forward',
    underlying: 'SC',
    direction: '买入',
    notional: 8000.00,
    marketValue: 400.00,
    delta: 1.00,
    gamma: 0.000,
    vega: 0.0,
    theta: 0.0,
    profitLoss: 80.00,
    maturityDate: '2024-03-30',
    status: 'active'
  },
  {
    contractId: 'OTC004',
    productType: 'structured',
    underlying: 'I',
    direction: '买入',
    notional: 12000.00,
    marketValue: 600.00,
    delta: 0.35,
    gamma: 0.003,
    vega: 25.2,
    theta: -3.1,
    profitLoss: -15.00,
    maturityDate: '2024-12-15',
    status: 'active'
  }
])

const getProductTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    option: 'primary',
    swap: 'success',
    forward: 'warning',
    structured: 'info'
  }
  return tagMap[type] || ''
}

const getProductTypeName = (type: string) => {
  const nameMap: Record<string, string> = {
    option: '期权',
    swap: '互换',
    forward: '远期',
    structured: '结构化产品'
  }
  return nameMap[type] || type
}

const getStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    active: 'success',
    expired: 'info',
    terminated: 'danger'
  }
  return tagMap[status] || ''
}

const getStatusName = (status: string) => {
  const nameMap: Record<string, string> = {
    active: '存续',
    expired: '到期',
    terminated: '提前终止'
  }
  return nameMap[status] || status
}

const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询完成')
  }, 1000)
}

const handleReset = () => {
  Object.assign(filterForm, {
    productType: '',
    underlying: '',
    status: ''
  })
}

const handleDetail = (row: any) => {
  ElMessage.info(`查看详情: ${row.contractId}`)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  handleSearch()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  handleSearch()
}
</script>

<style scoped>
.otc-positions-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.main-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.summary-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.profit {
  color: #67c23a;
  font-weight: bold;
}

.loss {
  color: #f56c6c;
  font-weight: bold;
}
</style>
