<template>
   <div class="user-management">
    <!-- 新增：左侧菜单区域 -->
    <el-row :gutter="20">
      <!-- 左侧菜单 -->
      <el-col :span="3">
        <el-menu
          :default-active="activeMenu"
          @select="handleMenuSelect"
          class="menu-style"
          background-color="#f9f9f9"
          text-color="#333"
          active-text-color="#409EFF"
        >
          <el-menu-item v-for="(item, index) in menuItems" :key="index" :index="item.id.toString()">
            <el-icon><component :is="item.icon" /></el-icon>
            <span style="margin-left: 10px;">{{ item.label }}</span>
          </el-menu-item>
        </el-menu>
      </el-col>

      <!-- 原有主内容区域包裹在 el-row 中 -->
      <el-col :span="21">
  <div class="menu-management">
    <h2>菜单管理</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="菜单名称">
              <el-input v-model="queryForm.menuName" placeholder="请输入菜单名称" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
              <el-button type="success" @click="handleAdd">新增</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" border style="width: 100%" row-key="id" default-expand-all>
        <el-table-column prop="menuName" label="菜单名称" width="200" />
        <el-table-column prop="menuDescription" label="菜单说明" />
        <el-table-column prop="reportTemplateNo" label="报表模板编号" width="150" />
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="success" size="small" @click="handleAddChild(scope.row)">添加</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/修改/添加菜单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item v-if="isAddChild" label="上级菜单编号" prop="parentId">
          <el-input v-model="formData.parentId" placeholder="上级菜单编号" :disabled="true" />
        </el-form-item>
        <el-form-item label="菜单名称" prop="menuName">
          <el-input v-model="formData.menuName" placeholder="请输入菜单名称" />
        </el-form-item>
        <el-form-item label="报表模板编号" prop="reportTemplateNo">
          <el-input v-model="formData.reportTemplateNo" placeholder="请输入报表模板编号" />
        </el-form-item>
        <el-form-item label="是否公开" prop="isPublic">
          <el-select v-model="formData.isPublic" placeholder="请选择" style="width: 100%;">
            <el-option label="是" value="是" />
            <el-option label="否" value="否" />
          </el-select>
        </el-form-item>
        <el-form-item label="链接类型" prop="linkType">
          <el-select v-model="formData.linkType" placeholder="请选择" style="width: 100%;">
            <el-option label="报表" value="报表" />
            <el-option label="其它" value="其它" />
          </el-select>
        </el-form-item>
        <el-form-item label="链接地址" prop="linkUrl">
          <el-input v-model="formData.linkUrl" placeholder="请输入链接地址" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formData.sort" :min="0" controls-position="right" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="菜单说明" prop="menuDescription">
          <el-input v-model="formData.menuDescription" placeholder="请输入菜单说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
  </el-col>
  </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'

import {
  User,
  Key,
  Menu as IconMenu
} from '@element-plus/icons-vue'

const activeMenu = ref('3')

const menuItems = ref([
  {
    id: 1,
    label: '用户管理',
    icon: User
  },
  {
    id: 2,
    label: '角色管理',
    icon: Key
  },
  {
    id: 3,
    label: '菜单管理',
    icon: IconMenu
  }
])

const router = useRouter()
// 菜单选择事件处理
const handleMenuSelect = (index) => {
  console.log('选中的菜单项:', index)

  if (index === '1') {
    router.push('/admin/user-management')
  } else if (index === '2') {
    router.push('/admin/role-management')
  } else if (index === '3') {
    router.push('/admin/menu-management')
  } 
}

// 查询表单
const queryForm = reactive({
  menuName: ''
})

// 生成菜单ID的计数器
let menuIdCounter = 100

// 表格数据
const tableData = ref([
  {
    id: 1,
    menuName: '特殊保证金申请管理',
    menuDescription: '特殊保证金相关业务管理',
    reportTemplateNo: 'RPT001',
    isPublic: '是',
    linkType: '报表',
    linkUrl: '/special-margin',
    sort: 1,
    children: [
      {
        id: 11,
        menuName: '特保模板管理',
        menuDescription: '管理特保模板',
        reportTemplateNo: 'RPT011',
        isPublic: '是',
        linkType: '报表',
        linkUrl: '/template-management',
        sort: 1
      },
      {
        id: 12,
        menuName: '特保申请参数配置',
        menuDescription: '配置申请参数',
        reportTemplateNo: 'RPT012',
        isPublic: '是',
        linkType: '报表',
        linkUrl: '/param-config',
        sort: 2
      },
      {
        id: 13,
        menuName: '模板化特保查询',
        menuDescription: '查询模板化特保',
        reportTemplateNo: 'RPT013',
        isPublic: '是',
        linkType: '报表',
        linkUrl: '/template-query',
        sort: 3
      },
      {
        id: 14,
        menuName: '个性化特保查询',
        menuDescription: '查询个性化特保',
        reportTemplateNo: 'RPT014',
        isPublic: '是',
        linkType: '报表',
        linkUrl: '/custom-query',
        sort: 4
      },
      {
        id: 15,
        menuName: '特殊保证金率申请',
        menuDescription: '申请特殊保证金率',
        reportTemplateNo: 'RPT015',
        isPublic: '是',
        linkType: '报表',
        linkUrl: '/special-rate-application',
        sort: 5
      }
    ]
  },
  {
    id: 2,
    menuName: '业务办理中心',
    menuDescription: '业务流程办理中心',
    reportTemplateNo: 'RPT002',
    isPublic: '是',
    linkType: '报表',
    linkUrl: '/business-center',
    sort: 2,
    children: [
      {
        id: 21,
        menuName: '发起流程',
        menuDescription: '发起业务流程',
        reportTemplateNo: 'RPT021',
        isPublic: '是',
        linkType: '报表',
        linkUrl: '/process-initiation',
        sort: 1
      },
      {
        id: 22,
        menuName: '流程明细--特殊保证金率申请',
        menuDescription: '查看流程明细',
        reportTemplateNo: 'RPT022',
        isPublic: '是',
        linkType: '报表',
        linkUrl: '/flow-details-special-rate',
        sort: 2
      },
      {
        id: 23,
        menuName: '影像归档--OA特保流程',
        menuDescription: '影像归档管理',
        reportTemplateNo: 'RPT023',
        isPublic: '是',
        linkType: '报表',
        linkUrl: '/image-archive-oa',
        sort: 3
      }
    ]
  },
  {
    id: 3,
    menuName: '系统设置',
    menuDescription: '系统管理设置',
    reportTemplateNo: 'RPT003',
    isPublic: '是',
    linkType: '报表',
    linkUrl: '/system-setting',
    sort: 3,
    children: [
      {
        id: 31,
        menuName: '菜单管理',
        menuDescription: '管理系统菜单',
        reportTemplateNo: 'RPT031',
        isPublic: '是',
        linkType: '报表',
        linkUrl: '/menu-management',
        sort: 1
      },
      {
        id: 32,
        menuName: '角色管理',
        menuDescription: '管理用户角色',
        reportTemplateNo: 'RPT032',
        isPublic: '是',
        linkType: '报表',
        linkUrl: '/role-management',
        sort: 2
      },
      {
        id: 33,
        menuName: '用户管理',
        menuDescription: '管理系统用户',
        reportTemplateNo: 'RPT033',
        isPublic: '是',
        linkType: '报表',
        linkUrl: '/user-management',
        sort: 3
      },
      {
        id: 34,
        menuName: '经营仪表盘',
        menuDescription: '经营数据仪表盘',
        reportTemplateNo: 'RPT034',
        isPublic: '是',
        linkType: '报表',
        linkUrl: '/dashboard',
        sort: 4
      }
    ]
  }
])

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const isAddChild = ref(false)

// 表单数据
const formData = reactive({
  id: null as number | null,
  parentId: null as number | null,
  menuName: '',
  reportTemplateNo: '',
  isPublic: '是',
  linkType: '报表',
  linkUrl: '',
  sort: 0,
  menuDescription: ''
})

// 表单验证规则
const formRules = {
  menuName: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
  sort: [{ required: true, message: '请输入排序', trigger: 'blur' }],
  menuDescription: [{ required: true, message: '请输入菜单说明', trigger: 'blur' }],
  isPublic: [{ required: true, message: '请选择是否公开', trigger: 'change' }],
  linkType: [{ required: true, message: '请选择链接类型', trigger: 'change' }]
}

// 生成菜单ID
const generateMenuId = () => {
  return ++menuIdCounter
}

// 查询
const handleQuery = () => {
  ElMessage.success('查询成功')
}

// 重置
const resetQuery = () => {
  Object.assign(queryForm, {
    menuName: ''
  })
}

// 导出
const handleExport = () => {
  ElMessage.success('导出成功')
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增菜单'
  isAddChild.value = false
  resetForm()
  formData.id = generateMenuId()
  dialogVisible.value = true
}

// 修改
const handleEdit = (row: any) => {
  dialogTitle.value = '修改菜单'
  isAddChild.value = false
  Object.assign(formData, {
    id: row.id,
    parentId: row.parentId,
    menuName: row.menuName,
    reportTemplateNo: row.reportTemplateNo,
    isPublic: row.isPublic,
    linkType: row.linkType,
    linkUrl: row.linkUrl,
    sort: row.sort,
    menuDescription: row.menuDescription
  })
  dialogVisible.value = true
}

// 添加下级菜单
const handleAddChild = (row: any) => {
  dialogTitle.value = '添加菜单'
  isAddChild.value = true
  resetForm()
  formData.parentId = row.id
  formData.id = generateMenuId()
  dialogVisible.value = true
}

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确认要删除菜单[${row.menuName}]？`, '提示', {
    confirmButtonText: '是',
    cancelButtonText: '否',
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
  })
}

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('保存成功')
      dialogVisible.value = false
    }
  })
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: null,
    parentId: null,
    menuName: '',
    reportTemplateNo: '',
    isPublic: '是',
    linkType: '报表',
    linkUrl: '',
    sort: 0,
    menuDescription: ''
  })
}

// 关闭对话框
const handleDialogClose = () => {
  resetForm()
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.menu-management {
  padding: 20px;
}

.query-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.query-form {
  margin: 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.menu-style {
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #f9f9f9;
  padding: 10px 0;
}
</style>
