<template>
  <SubHeader/>
  <div class="report-table">
    <el-row :gutter="20">
      <!-- 左侧菜单 -->
      <el-col :span="2">
         <el-menu
          :default-active="activeMenu"
          @select="handleMenuSelect"
          class="menu-style"
          unique-opened
        >
          <!-- 遍历一级菜单 -->
          <template v-for="item in menuItems" :key="item.id">
            <!-- 如果没有子菜单，则直接显示一级菜单项 -->
            <el-menu-item v-if="!item.children" :index="item.id">
              <span>{{ item.label }}</span>
            </el-menu-item>

            <!-- 如果有子菜单，则显示为 el-sub-menu -->
            <el-sub-menu v-else :index="item.id">
              <template #title>
                <span>{{ item.label }}</span>
              </template>

              <!-- 遍历二级菜单 -->
              <el-menu-item
                v-for="child in item.children"
                :key="child.id"
                :index="child.id"
              >
                <span style="margin-left: 10px;">{{ child.label }}</span>
              </el-menu-item>
            </el-sub-menu>
          </template>
        </el-menu>
      </el-col>

      <!-- 右侧内容 -->
      <el-col :span="22">
        <!-- 检索区域 -->
        <div class="filter-bar">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"               
                style="width: 100%;"
              />
            </el-col>
            <el-col :span="6">
              <el-select
                v-model="selectedCategory"
                placeholder="报告类型"
                clearable
                style="width: 100%;"
              >
                <el-option
                  v-for="item in categories"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select
                v-model="selectedStatus"
                placeholder="状态筛选"
                clearable
                style="width: 100%;"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索报告标题/内容"
                clearable
              >
                <template #append>
                  <el-button :icon="Search" />
                </template>
              </el-input>
            </el-col>
          </el-row>
        </div>
        <div v-if="reports.length > 0">
          <el-card
            v-for="(report, index) in reports"
            :key="index"
            shadow="hover"
            style="margin-bottom: 20px;"
          >
            <!-- 卡片标题 -->
            <template #header>
              <div
                style="font-size: 16px; font-weight: bold; text-align: left; cursor: pointer;"
                @click="handleTitleClick(report)"
              >
                {{ report.title }}
              </div>
            </template>

            <!-- 第二行：作者、日期、分类 -->
            <div style="display: flex; gap: 20px; margin-bottom: 10px; color: #666;">
              <span>作者：{{ report.author }}</span>
              <span>日期：{{ report.date }}</span>
              <span>分类：{{ report.category }}</span>
            </div>

            <!-- 第三行：摘要 -->
            <div style="margin-bottom: 10px; color: #333; text-align: left;">
              {{ report.summary }}
            </div>

            <!-- 第四行：标签 -->
            <div style="text-align: left;">
              <el-tag v-for="(tag, idx) in report.tags" :key="idx" size="small">{{ tag }}</el-tag>
            </div>
          </el-card>
        </div>
        <p v-else>暂无研报数据</p>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import SubHeader from '~/components/layouts/SubHeader.vue'
import { ref, onMounted } from 'vue'
import {Search, Postcard, DocumentAdd } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router';

const router = useRouter();
// 标题点击事件
const handleTitleClick = (report: Report) => {
  ElMessage.info(`你点击了标题：${report.title}`)
}

// 菜单相关
const activeMenu = ref('1')

const availableIcons = [
  Postcard,
  DocumentAdd
]

const menuItems = ref([
  {
    id: '0',
    label: '逻辑推演'
  },
  {
    id: '1',
    label: '研报列表',

    children: [
      { id: '2-1', label: '周报' },
      { id: '2-2', label: '月报' },
      { id: '2-3', label: '年报' },
      { id: '2-4', label: '专题' },
      { id: '2-5', label: '调研' },
      { id: '2-6', label: '策略' }
    ]
  },
  {
    id: '2',
    label: '数据图表',
  }
  // {
  //   id: '2',
  //   label: '研报管理',
  //   icon: availableIcons[1], // 可选图标
  //   children: [
  //     { id: '2-1', label: '上传研报' },
  //     { id: '2-2', label: '系统制作研报' }
  //   ]
  // }
]);

// 菜单选择事件处理
const handleMenuSelect = (index: string) => {
  console.log('选中的菜单项:', index)

  if (index === '0') {
    router.push('/logical-deduction')
  } else if (index === '1') {
    router.push('/report')
  } else if (index.startsWith('2-')) {
    router.push('/report')
  } else if (index === '2') {
    router.push('/data-chart')
  }
}

// 筛选条件
const dateRange = ref<string[]>([])
const selectedCategory = ref('')
const selectedStatus = ref('')
const searchKeyword = ref('')

// 分类选项
const categories = [
  { value: '科技', label: '科技' },
  { value: '金融', label: '金融' },
  { value: '医疗', label: '医疗' },
  { value: '能源', label: '能源' },
  { value: '消费', label: '消费' }
]

// 状态选项
const statusOptions = [
  { value: '已发布', label: '已发布' },
  { value: '草稿', label: '草稿' },
  { value: '归档', label: '归档' }
]

// 定义研报数据的类型
interface Report {
  title: string
  author: string
  date: string
  summary: string
}

// const reports = ref<Report[]>([]) // 显式指定类型
// 定义研报数据的类型
interface Report {
  title: string
  author: string
  date: string
  summary: string
  category: string // 分类
  tags: string[]   // 品种类标签
}

const reports = ref<Report[]>([])

// 随机生成数据函数
const generateRandomReports = () => {
  const sampleTitles = [
    '人工智能发展趋势',
    '新能源汽车市场分析',
    '2024年宏观经济展望',
    '区块链技术应用研究',
    '云计算与大数据发展现状'
  ]
  const sampleAuthors = ['张三', '李四', '王五', '赵六', '陈七']
  const sampleSummaries = [
    '本报告分析了AI技术的最新进展及行业落地情况。',
    '对新能源汽车行业政策和市场的全面解读。',
    '预测2024年中国及全球经济的主要趋势走向。',
    '探讨区块链在金融、供应链等领域的应用场景。',
    '总结当前云计算和大数据技术的发展现状与挑战。'
  ]
  const categories = ['科技', '金融', '医疗', '能源', '消费']
  const tags = ['AI', '区块链', '新能源', '大数据', '云计算']

  const newReports: Report[] = []
  for (let i = 0; i < 5; i++) {
    newReports.push({
      title: sampleTitles[Math.floor(Math.random() * sampleTitles.length)],
      author: sampleAuthors[Math.floor(Math.random() * sampleAuthors.length)],
      date: getRandomDate(new Date(2023, 0, 1), new Date()),
      summary: sampleSummaries[Math.floor(Math.random() * sampleSummaries.length)],
      category: categories[Math.floor(Math.random() * categories.length)],
      tags: [tags[Math.floor(Math.random() * tags.length)]]
    })
  }

  reports.value = newReports
}

// 生成随机日期函数
const getRandomDate = (start: Date, end: Date): string => {
  const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
  return date.toISOString().split('T')[0]
}

// 页面加载时自动获取数据
onMounted(() => {
  generateRandomReports()
})
</script>

<style scoped lang="scss">
.menu-style {
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #f9f9f9;
  padding: 10px 0;
}

.menu-style .el-menu-item {
  padding-left: 30px !important;
}

.report-table {
  padding: 20px;
}

.report-table {
  padding: 20px;
}

.report-table .el-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.report-table .el-card__header {
  background-color: #f5f7fa;
  padding: 12px 20px;
}

.report-table .el-card__body {
  padding: 15px 20px;
}

.filter-bar {
  padding-bottom: 20px;
}
</style>