<template>
  <SubHeader />
  <div class="report-table">
    <el-row :gutter="20">
      <!-- 左侧菜单 -->
      <el-col :span="2">
        <el-menu
          :default-active="activeMenu"
          @select="handleMenuSelect"
          class="menu-style"
          unique-opened
        >
          <template v-for="item in menuItems" :key="item.id">
            <el-menu-item v-if="!item.children" :index="item.id">
              <span>{{ item.label }}</span>
            </el-menu-item>

            <el-sub-menu v-else :index="item.id">
              <template #title>
                <span>{{ item.label }}</span>
              </template>

              <el-menu-item
                v-for="child in item.children"
                :key="child.id"
                :index="child.id"
              >
                <span style="margin-left: 10px;">{{ child.label }}</span>
              </el-menu-item>
            </el-sub-menu>
          </template>
        </el-menu>
      </el-col>

      <!-- 右侧内容 -->
      <el-col :span="20">
        <!-- 研报列表区域（完全复制 index.vue） -->
   
        <!-- 筛选条件 -->
        <div class="filter-bar">
            <el-row :gutter="20">
                
                <el-col :span="6">
                <el-select
                    v-model="selectedStatus"
                    placeholder="指标来源"
                    clearable
                    @change="handleFilterChange"
                >
                    <el-option
                    v-for="item in statusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    />
                </el-select>
                </el-col>
                <el-col :span="6">
                <el-input
                    v-model="searchKeyword"
                    placeholder="指标名称"
                    clearable
                    @input="handleSearch"
                >
                    <template #append>
                    <el-button :icon="Search" />
                    </template>
                </el-input>
             </el-col>
            </el-row>
        </div>

        <!-- 报告列表 -->
        <div class="report-list">
        <template v-if="reportList.length > 0">
            <el-row :gutter="20">
              <el-col
                v-for="(report, index) in reportList"
                :key="report.id"
                :xs="24"
                :sm="12"
                :md="8"
                :lg="6"
                >
                <IndicatorItemCard
                    :indicator="report"
                    :index="index"
                    @click="handleReportClick(report)"
                    @download="handleDownload"
                    @favorite="handleFavorite"
                    @open-full-chart="openFullScreenChart"
                />
            </el-col>
            </el-row>
        </template>
        <el-empty v-else description="暂无报告数据" />
        </div>
       
      </el-col>
    </el-row>
  </div>
 <!-- 全屏图表弹窗 -->
 <el-dialog v-model="isDialogVisible" title="详细图表" width="80%">
  <el-tabs v-model="activeTab" type="card">
    <el-tab-pane label="图表" name="chart">
      <div id="full-chart" class="full-chart" style="width: 100%; height: 500px;"></div>
    </el-tab-pane>
    <el-tab-pane label="表格" name="table">
      <el-table :data="chartTableData" border style="width: 100%">
        <el-table-column prop="date" label="日期" align="center"></el-table-column>
        <el-table-column prop="value" label="数值" align="center"></el-table-column>
      </el-table>
    </el-tab-pane>
  </el-tabs>
 </el-dialog>
</template>

<script lang="ts" setup>
import { ref, nextTick } from 'vue'
import * as echarts from 'echarts'

import SubHeader from '~/components/layouts/SubHeader.vue'
import IndicatorItemCard from '~/components/indicator/IndicatorItemCard.vue'
import { Search, Postcard } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()

// ===================== 全屏图表 ==================
const isDialogVisible = ref(false)
let fullChart = null

const activeTab = ref('chart') // 默认显示图表页
const chartTableData = ref([]) // 表格数据源

// 打开全屏图表的方法
const openFullScreenChart = (indicator) => {
  isDialogVisible.value = true

  // 构建测试数据
  const data = Array.from({ length: 30 }, () => Math.floor(Math.random() * 100))
  const xAxisData = Array.from({ length: 30 }, (_, i) => `${i + 1}日`)
  chartTableData.value = data.map((value, index) => ({
    date: xAxisData[index],
    value
  }))

  nextTick(() => {
    if (!fullChart) {
      const chartDom = document.getElementById('full-chart')
      if (!chartDom) return
      fullChart = echarts.init(chartDom)

      const isLineChart = Number(indicator.id) % 2 === 1

      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: (params) => `${params[0].name}<br/>数值: ${params[0].value}`
        },
        grid: {
          top: 40,
          bottom: 40,
          left: 60,
          right: 20
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLine: { lineStyle: { color: '#ccc' } },
          axisLabel: { color: '#666' }
        },
        yAxis: {
          type: 'value',
          name: '数值',
          axisLine: { lineStyle: { color: '#ccc' } },
          axisLabel: { color: '#666' }
        },
        series: [{
          type: isLineChart ? 'line' : 'bar',
          smooth: isLineChart,
          data: data,
          lineStyle: { color: '#409EFF' },
          itemStyle: { color: '#409EFF' },
          areaStyle: isLineChart ? { color: 'rgba(64, 158, 255, 0.2)' } : undefined,
          symbol: 'none'
        }]
      }

      fullChart.setOption(option)
    } else {
      fullChart.resize()
      fullChart.setOption({
        series: [{
          data: chartTableData.value.map(d => d.value)
        }]
      })
    }
  })
}

// ================== 菜单相关 ==================
interface MenuItem {
  id: string
  label: string
  children?: MenuItem[]
}

const activeMenu = ref('0-1')

const menuItems = ref<MenuItem[]>([
  {
    id: '0',
    label: '指标',
    children: [
      { id: '0-1', label: '同花顺' },
      { id: '0-2', label: '卓创' },
      { id: '0-3', label: '涌溢' },
      { id: '0-4', label: '百川' },
      { id: '0-5', label: '钢联' },
      { id: '0-6', label: '本地' }
    ]
  },
  {
    id: '1',
    label: '研报列表'
  },
  {
    id: '2',
    label: '数据图表'
  }
])

const handleMenuSelect = (index: string) => {
  console.log('选中的菜单项:', index)

  if (index === '0') {
    router.push('/logical-deduction')
  } else if (index === '1') {
    router.push('/report')
  } else if (index.startsWith('2-')) {
    router.push('/report')
  } else if (index === '2') {
    router.push('/data-chart')
  }
}

// ================== 研报相关数据 ==================
// 筛选条件
const dateRange = ref<string[]>([])
const selectedCategory = ref('')
const selectedStatus = ref('')
const searchKeyword = ref('')

// 模拟数据选项
const categories = [
  { value: 'macro', label: '宏观报告' },
  { value: 'commodity', label: '商品期货' },
  { value: 'financial', label: '金融期货' },
  { value: 'strategy', label: '投资策略' }
]

const statusOptions = [
  { value: 'ths', label: '同花顺' },
  { value: 'zc', label: '卓创' },
  { value: 'yy', label: '涌溢' },
  { value: 'bc', label: '百川' },
  { value: 'gl', label: '钢联' },
  { value: 'local', label: '本地' },
]

// 测试数据
// 测试数据
const mockReports = [
  {
    id: 1,
    name: '宏观经济指数',
    isFavorite: true,
    datasource: 'ths',
    category: '月',
    categoryLabel: '月度',
    boardName: '宏观板块',
    productName: '综合指数',
    latestValue: Number((Math.random() * 1000).toFixed(2)), // 最新值，保留两位小数
    changeRate: Number(((Math.random() * 40 - 20)).toFixed(2)) // 变化率范围 [-20, 20]，保留两位小数
  },
  {
    id: 2,
    name: '铜期货价格走势',
    isFavorite: false,
    datasource: 'zc',
    category: '周',
    categoryLabel: '周度',
    boardName: '有色金属',
    productName: '铜',
    latestValue: Number((Math.random() * 1000).toFixed(2)),
    changeRate: Number(((Math.random() * 40 - 20)).toFixed(2))
  },
  {
    id: 3,
    name: '股指期货策略建议',
    isFavorite: true,
    datasource: 'yy',
    category: '日',
    categoryLabel: '每日',
    boardName: '金融板块',
    productName: '沪深300',
    latestValue: Number((Math.random() * 1000).toFixed(2)),
    changeRate: Number(((Math.random() * 40 - 20)).toFixed(2))
  },
  {
    id: 4,
    name: '农产品套利机会分析',
    isFavorite: false,
    datasource: 'bc',
    category: '季度',
    categoryLabel: '季度',
    boardName: '农产品',
    productName: '玉米',
    latestValue: Number((Math.random() * 1000).toFixed(2)),
    changeRate: Number(((Math.random() * 40 - 20)).toFixed(2))
  },
  {
    id: 5,
    name: '钢铁行业库存趋势',
    isFavorite: true,
    datasource: 'gl',
    category: '年',
    categoryLabel: '年度',
    boardName: '黑色系',
    productName: '螺纹钢',
    latestValue: Number((Math.random() * 1000).toFixed(2)),
    changeRate: Number(((Math.random() * 40 - 20)).toFixed(2))
  },
  {
    id: 6,
    name: '本地经济运行监测',
    isFavorite: false,
    datasource: 'local',
    category: '月',
    categoryLabel: '月度',
    boardName: '本地经济',
    productName: '综合',
    latestValue: Number((Math.random() * 1000).toFixed(2)),
    changeRate: Number(((Math.random() * 40 - 20)).toFixed(2))
  }
]

const reportListData = ref(mockReports)
const reportList = ref(reportListData.value.slice(0, 10))
const totalReports = ref(reportListData.value.length)

// 当前页码和每页显示数量
const currentPage = ref(1)
const pageSize = ref(10)

// 加载更多数据
function loadMoreReports() {
  ElMessage.info('已加载全部数据')
}

function loadReports() {
  const keyword = searchKeyword.value.toLowerCase()
  const category = selectedCategory.value
  const status = selectedStatus.value

  let filtered = reportListData.value.filter((report) => {
    return (
      (!category || report.category === category) &&
      (!status || report.status === status) &&
      (!keyword ||
        report.title.toLowerCase().includes(keyword) ||
        report.summary.toLowerCase().includes(keyword))
    )
  })

  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value

  // 追加新数据到现有列表
  if (currentPage.value > 1) {
    reportList.value = [...reportList.value, ...filtered.slice(start, end)]
  } else {
    reportList.value = filtered.slice(start, end)
  }

  totalReports.value = filtered.length
}

function handleFilterChange() {
  currentPage.value = 1
  loadReports()
}

function handleDateChange() {
  handleFilterChange()
}

function handleSearch() {
  handleFilterChange()
}

function handleReportClick(report) {
  console.log('打开报告详情:', report)
  router.push({
    path: `/report-detail`
  })
}

function handleDownload(report) {
  console.log('下载报告:', report)
}

function handleFavorite(report) {
  console.log('收藏报告:', report)
}
</script>

<style scoped>
.card-title {
  font-size: 24px;
  font-weight: bold;
}

.report-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.filter-bar {
  margin-top: 20px;
  margin-bottom: 20px;
}

.report-list {
  margin-bottom: 20px;
}
</style>