<template>
  <el-menu default-active="1-1" class="el-menu-vertical-demo" @open="handleOpen" @close="handleClose" :collapse="isCollapse">
    <template v-for="item in menuData">
      <template v-if="item.children">
        <el-sub-menu :index="item.index" :key="item.index">
          <template #title>
            <span>{{ item.label }}</span>
          </template>
          <SidebarItem :menuData="item.children"></SidebarItem>
        </el-sub-menu>
      </template>
      <template v-else>
        <el-menu-item :index="item.index" :key="item.index">
          <span>{{ item.label }}</span>
        </el-menu-item>
      </template>
    </template>
  </el-menu>
</template>

<script setup>
import { ref, defineProps } from 'vue';
import SidebarItem from './SidebarItem.vue';

const props = defineProps({
  menuData: {
    type: Array,
    required: true,
  },
});

const isCollapse = ref(false);
const handleOpen = (key, keyPath) => {
  console.log(key, keyPath);
};
const handleClose = (key, keyPath) => {
  console.log(key, keyPath);
};
</script>

<style scoped>
.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 200px;
  min-height: 400px;
}
</style>
