<!-- src/pages/variety-analysis.vue -->
<template>
  <SubHeader />
  <div class="variety-analysis-container">
    <!-- 左侧菜单 -->
    <ProductAnalysisSide />

    <!-- 主体内容区域 -->
    <div class="content">
      <!-- 新增 el-card 区域 -->
      <el-card class="chart-card">
        <el-row :gutter="20">
          <!-- 左侧图表区域 -->
          <el-col :span="16">
            <div ref="chart" class="chart"></div>
          </el-col>

          <!-- 右侧表单区域 -->
          <el-col :span="8">
            <el-form :model="chartSettings" label-width="100px">
              <!-- 图表标题输入框 -->
              <el-form-item label="图表标题">
                <el-input v-model="chartTitle" placeholder="请输入图表标题" @input="updateChart"></el-input>
              </el-form-item>

              <!-- 图表类型选择和颜色选择 -->
              <el-form-item v-for="(item, index) in chartSettings.series" :key="index" :label="'Series ' + (index + 1)">
                <el-select v-model="item.type" placeholder="选择图表类型" @change="updateChart" style="width: 120px; margin-right: 10px;">
                  <el-option label="线图" value="line"></el-option>
                  <el-option label="柱状图" value="bar"></el-option>
                </el-select>
                <el-color-picker v-model="item.color" @change="updateChart"></el-color-picker>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

import SubHeader from '~/components/layouts/SubHeader.vue'
// 引入左侧菜单组件
import ProductAnalysisSide from '~/components/layouts/ProductAnalysisSide.vue'

// 图表实例
const chart = ref(null)

// 表单数据模型
const chartSettings = ref({
  series: [
    { type: 'line', color: '#5470c6', data: [] },
    { type: 'bar', color: '#91cc75', data: [] },
    { type: 'line', color: '#fac858', data: [] }
  ]
})

// 图表标题
const chartTitle = ref('默认图表标题')

// 初始化图表
onMounted(() => {
  // 初始化随机数据
  chartSettings.value.series.forEach((item) => {
    item.data = Array.from({ length: 7 }, () => Math.random() * 100)
  })
  renderChart()
})

// 渲染图表
function renderChart() {
  const chartInstance = echarts.init(chart.value)
  const option = {
    title: {
      text: chartTitle.value, // 动态绑定标题
      left: 'left'
    },
    tooltip: { trigger: 'axis' },
    legend: { data: ['Series 1', 'Series 2', 'Series 3'] },
    xAxis: { type: 'category', data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] },
    yAxis: { type: 'value' },
    series: chartSettings.value.series.map((item, index) => ({
      name: `Series ${index + 1}`,
      type: item.type,
      data: item.data, // 使用存储的数据
      itemStyle: {
        color: item.color // 动态绑定颜色
      }
    }))
  }
  chartInstance.setOption(option)
}

// 实时更新图表
watch(
  () => ({ ...chartSettings.value, title: chartTitle.value }),
  () => {
    renderChart()
  },
  { deep: true }
)
</script>

<style scoped>
.variety-analysis-container {
  display: flex;
  height: 100vh;
  padding: 20px;
}

.content {
  flex: 1;
  padding: 20px;
  background-color: #fff;
  margin-left: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  white-space: nowrap; /* 禁止换行 */
  overflow-x: auto; /* 如果内容超出，允许横向滚动 */
}

.chart-card {
  margin-top: 20px;
}

.chart {
  width: 100%;
  height: 400px;
}
</style>