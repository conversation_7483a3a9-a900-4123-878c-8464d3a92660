<template>
  <template v-for="item in menuData">
    <template v-if="item.children">
      <el-sub-menu :index="item.index" :key="item.index">
        <template #title>
          <span>{{ item.label }}</span>
        </template>
        <SidebarItem :menuData="item.children"></SidebarItem>
      </el-sub-menu>
    </template>
    <template v-else>
      <el-menu-item :index="item.index" :key="item.index">
        <span>{{ item.label }}</span>
      </el-menu-item>
    </template>
  </template>
</template>

<script setup>
import { defineProps } from 'vue';

defineProps({
  menuData: {
    type: Array,
    required: true,
  },
});
</script>
