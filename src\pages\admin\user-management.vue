<template>
  <div class="user-management">
    <!-- 新增：左侧菜单区域 -->
    <el-row :gutter="20">
      <!-- 左侧菜单 -->
      <el-col :span="3">
        <el-menu
          :default-active="activeMenu"
          @select="handleMenuSelect"
          class="menu-style"
          background-color="#f9f9f9"
          text-color="#333"
          active-text-color="#409EFF"
        >
          <el-menu-item v-for="(item, index) in menuItems" :key="index" :index="item.id.toString()">
            <el-icon><component :is="item.icon" /></el-icon>
            <span style="margin-left: 10px;">{{ item.label }}</span>
          </el-menu-item>
        </el-menu>
      </el-col>

      <!-- 原有主内容区域包裹在 el-row 中 -->
      <el-col :span="21">
      <div class="user-management">
        <h2>用户管理</h2>

        <!-- 查询区域 -->
        <el-card class="query-card">
          <el-form :model="queryForm" class="query-form" label-width="140px" label-position="left">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="组织名称">
                  <el-select v-model="queryForm.organizationName" placeholder="请选择组织" clearable style="width: 100%;">
                    <el-option label="长江期货" value="长江期货" />
                    <el-option label="长江证券" value="长江证券" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所属部门">
                  <el-tree-select
                    v-model="queryForm.departments"
                    :data="departmentTreeData"
                    :props="{ label: 'name', value: 'code', children: 'children' }"
                    placeholder="请选择部门"
                    multiple
                    filterable
                    check-strictly
                    clearable
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所属角色">
                  <el-select v-model="queryForm.roleId" placeholder="请选择角色" clearable style="width: 100%;">
                    <el-option label="所有" value="" />
                    <el-option label="系统管理员" value="1" />
                    <el-option label="业务管理员" value="2" />
                    <el-option label="风控专员" value="3" />
                    <el-option label="普通用户" value="4" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="OA账号">
                  <el-input v-model="queryForm.oaAccount" placeholder="请输入OA账号" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="OA名称">
                  <el-input v-model="queryForm.oaName" placeholder="请输入OA名称" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="数据来源">
                  <el-select v-model="queryForm.dataSource" placeholder="请选择数据来源" clearable style="width: 100%;">
                    <el-option label="每晚批量同步" value="每晚批量同步" />
                    <el-option label="临时人工授权" value="临时人工授权" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <div class="button-group">
                  <el-button type="primary" @click="handleQuery">查询</el-button>
                  <el-button @click="resetQuery">重置</el-button>
                  <el-button type="success" @click="handleAdd">新增</el-button>
                  <el-button type="warning" @click="handleExport">导出</el-button>
                </div>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <!-- 表格区域 -->
        <el-card class="table-card">
          <el-table :data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)" border style="width: 100%">
            <el-table-column prop="organizationName" label="组织名称" width="120" />
            <el-table-column prop="departmentCode" label="部门编码" width="120" />
            <el-table-column prop="departmentName" label="所属部门" width="150" />
            <el-table-column prop="oaAccount" label="OA账号" width="120" />
            <el-table-column prop="oaName" label="OA名称" width="120" />
            <el-table-column prop="employeeCode" label="员工编码" width="120" />
            <el-table-column prop="mainRoleName" label="所属角色" width="120" />
            <el-table-column prop="phone" label="手机号码" width="130" />
            <el-table-column prop="accessPermission" label="访问权限" width="100" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.accessPermission === '是' ? 'success' : 'danger'">
                  {{ scope.row.accessPermission }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="dataSource" label="数据来源" width="130" />
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="scope">
                <el-button type="primary" size="small" @click="handleEdit(scope.row)">修改</el-button>
                <el-button type="warning" size="small" @click="handleRole(scope.row)">角色</el-button>
                <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableData.length"
              :pager-count="7"
            />
          </div>
        </el-card>

        <!-- 新增/修改用户对话框 -->
        <el-dialog
          v-model="dialogVisible"
          :title="dialogTitle"
          width="600px"
          @close="handleDialogClose"
        >
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="组织名称" prop="organizationName">
                  <el-select v-model="formData.organizationName" placeholder="请选择组织" style="width: 100%;">
                    <el-option label="长江期货" value="长江期货" />
                    <el-option label="长江证券" value="长江证券" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="部门编码" prop="departmentCode">
                  <el-input v-model="formData.departmentCode" placeholder="请输入部门编码" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="所属部门" prop="departmentName">
                  <el-tree-select
                    v-model="formData.departmentName"
                    :data="departmentTreeData"
                    :props="{ label: 'name', value: 'name', children: 'children' }"
                    placeholder="请选择部门"
                    filterable
                    check-strictly
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="OA账号" prop="oaAccount">
                  <el-input
                    v-model="formData.oaAccount"
                    placeholder="请输入OA账号"
                    :disabled="isEdit"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="OA名称" prop="oaName">
                  <el-input v-model="formData.oaName" placeholder="请输入OA名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="员工编码" prop="employeeCode">
                  <el-input v-model="formData.employeeCode" placeholder="请输入员工编码" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="手机号码" prop="phone">
                  <el-input v-model="formData.phone" placeholder="请输入手机号码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="访问权限" prop="accessPermission">
                  <el-radio-group v-model="formData.accessPermission">
                    <el-radio value="是">是</el-radio>
                    <el-radio value="否">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="数据来源" prop="dataSource">
                  <el-radio-group v-model="formData.dataSource">
                    <el-radio value="每晚批量同步">每晚批量同步</el-radio>
                    <el-radio value="临时人工授权">临时人工授权</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <template #footer>
            <div class="dialog-footer">
              <el-button @click="dialogVisible = false">关闭</el-button>
              <el-button type="primary" @click="handleSubmit" :disabled="!formData.oaAccount">保存</el-button>
            </div>
          </template>
        </el-dialog>

        <!-- 绑定用户角色对话框 -->
        <el-dialog
          v-model="roleDialogVisible"
          title="绑定用户角色"
          width="600px"
        >
          <el-table :data="roleTableData" border style="width: 100%">
            <el-table-column width="50" align="center">
              <template #default="scope">
                <el-checkbox
                  v-model="scope.row.checked"
                  @change="handleRoleCheck(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="roleCode" label="角色编号" width="120" />
            <el-table-column prop="roleName" label="角色名称" width="150" />
            <el-table-column label="是否主角色" width="120" align="center">
              <template #default="scope">
                <el-radio
                  v-model="mainRoleId"
                  :value="scope.row.id"
                  :disabled="!scope.row.checked"
                  @change="handleMainRoleChange"
                >
                  主角色
                </el-radio>
              </template>
            </el-table-column>
          </el-table>
          <template #footer>
            <div class="dialog-footer">
              <el-button @click="roleDialogVisible = false">关闭</el-button>
              <el-button type="primary" @click="handleRoleSubmit">保存</el-button>
            </div>
          </template>
        </el-dialog>
      </div>
    </el-col>
  </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'

import {
  User,
  Key,
  Menu as IconMenu
} from '@element-plus/icons-vue'

const activeMenu = ref('1')

const menuItems = ref([
  {
    id: 1,
    label: '用户管理',
    icon: User
  },
  {
    id: 2,
    label: '角色管理',
    icon: Key
  },
  {
    id: 3,
    label: '菜单管理',
    icon: IconMenu
  }
])

const router = useRouter()
// 菜单选择事件处理
const handleMenuSelect = (index) => {
  console.log('选中的菜单项:', index)

  if (index === '1') {
    router.push('/admin/user-management')
  } else if (index === '2') {
    router.push('/admin/role-management')
  } else if (index === '3') {
    router.push('/admin/menu-management')
  } 
}

// 查询表单
const queryForm = reactive({
  organizationName: '',
  departments: [],
  roleId: '',
  oaAccount: '',
  oaName: '',
  dataSource: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 部门树数据
const departmentTreeData = ref([
  {
    code: 'DEPT001',
    name: '总经理办公室',
    children: [
      { code: 'DEPT001001', name: '董事会秘书处' },
      { code: 'DEPT001002', name: '法务合规部' }
    ]
  },
  {
    code: 'DEPT002',
    name: '风险管理部',
    children: [
      { code: 'DEPT002001', name: '市场风险管理部' },
      { code: 'DEPT002002', name: '信用风险管理部' },
      { code: 'DEPT002003', name: '操作风险管理部' }
    ]
  },
  {
    code: 'DEPT003',
    name: '业务部门',
    children: [
      { code: 'DEPT003001', name: '经纪业务部' },
      { code: 'DEPT003002', name: '资产管理部' },
      { code: 'DEPT003003', name: '投资咨询部' }
    ]
  },
  {
    code: 'DEPT004',
    name: '技术部门',
    children: [
      { code: 'DEPT004001', name: '信息技术部' },
      { code: 'DEPT004002', name: '系统运维部' }
    ]
  }
])

// 表格数据
const tableData = ref([
  {
    id: 1,
    organizationName: '长江期货',
    departmentCode: 'DEPT001001',
    departmentName: '董事会秘书处',
    oaAccount: 'admin001',
    oaName: '张三',
    employeeCode: 'EMP001',
    mainRoleName: '系统管理员',
    phone: '***********',
    accessPermission: '是',
    dataSource: '每晚批量同步'
  },
  {
    id: 2,
    organizationName: '长江期货',
    departmentCode: 'DEPT002001',
    departmentName: '市场风险管理部',
    oaAccount: 'risk001',
    oaName: '李四',
    employeeCode: 'EMP002',
    mainRoleName: '风控专员',
    phone: '***********',
    accessPermission: '是',
    dataSource: '每晚批量同步'
  },
  {
    id: 3,
    organizationName: '长江证券',
    departmentCode: 'DEPT003001',
    departmentName: '经纪业务部',
    oaAccount: 'broker001',
    oaName: '王五',
    employeeCode: 'EMP003',
    mainRoleName: '业务管理员',
    phone: '***********',
    accessPermission: '是',
    dataSource: '临时人工授权'
  },
  {
    id: 4,
    organizationName: '长江期货',
    departmentCode: 'DEPT004001',
    departmentName: '信息技术部',
    oaAccount: 'tech001',
    oaName: '赵六',
    employeeCode: 'EMP004',
    mainRoleName: '普通用户',
    phone: '***********',
    accessPermission: '否',
    dataSource: '每晚批量同步'
  },
  {
    id: 5,
    organizationName: '长江证券',
    departmentCode: 'DEPT003002',
    departmentName: '资产管理部',
    oaAccount: 'asset001',
    oaName: '钱七',
    employeeCode: 'EMP005',
    mainRoleName: '业务管理员',
    phone: '***********',
    accessPermission: '是',
    dataSource: '临时人工授权'
  }
])

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const isEdit = ref(false)

// 角色对话框
const roleDialogVisible = ref(false)
const mainRoleId = ref<number | null>(null)

// 表单数据
const formData = reactive({
  id: null,
  organizationName: '',
  departmentCode: '',
  departmentName: '',
  oaAccount: '',
  oaName: '',
  employeeCode: '',
  phone: '',
  accessPermission: '是',
  dataSource: '每晚批量同步'
})

// 角色表格数据
const roleTableData = ref([
  {
    id: 1,
    roleCode: 'ROLE001',
    roleName: '系统管理员',
    checked: false
  },
  {
    id: 2,
    roleCode: 'ROLE002',
    roleName: '业务管理员',
    checked: false
  },
  {
    id: 3,
    roleCode: 'ROLE003',
    roleName: '风控专员',
    checked: false
  },
  {
    id: 4,
    roleCode: 'ROLE004',
    roleName: '普通用户',
    checked: false
  }
])

// 表单验证规则
const formRules = {
  organizationName: [{ required: true, message: '请选择组织名称', trigger: 'change' }],
  departmentCode: [{ required: true, message: '请输入部门编码', trigger: 'blur' }],
  oaAccount: [{ required: true, message: '请输入OA账号', trigger: 'blur' }],
  oaName: [{ required: true, message: '请输入OA名称', trigger: 'blur' }],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ]
}

// 查询
const handleQuery = () => {
  ElMessage.success('查询成功')
}

// 重置
const resetQuery = () => {
  Object.assign(queryForm, {
    organizationName: '',
    departments: [],
    roleId: '',
    oaAccount: '',
    oaName: '',
    dataSource: ''
  })
}

// 导出
const handleExport = () => {
  ElMessage.success('导出成功')
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增用户'
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 修改
const handleEdit = (row: any) => {
  dialogTitle.value = '修改用户'
  isEdit.value = true
  Object.assign(formData, {
    id: row.id,
    organizationName: row.organizationName,
    departmentCode: row.departmentCode,
    departmentName: row.departmentName,
    oaAccount: row.oaAccount,
    oaName: row.oaName,
    employeeCode: row.employeeCode,
    phone: row.phone,
    accessPermission: row.accessPermission,
    dataSource: row.dataSource
  })
  dialogVisible.value = true
}

// 角色管理
const handleRole = (row: any) => {
  // 重置角色选择状态
  roleTableData.value.forEach(role => {
    role.checked = false
  })
  mainRoleId.value = null

  // 模拟获取用户已有角色（这里假设用户有一些角色）
  if (row.mainRoleName === '系统管理员') {
    roleTableData.value[0].checked = true
    mainRoleId.value = 1
  } else if (row.mainRoleName === '业务管理员') {
    roleTableData.value[1].checked = true
    mainRoleId.value = 2
  } else if (row.mainRoleName === '风控专员') {
    roleTableData.value[2].checked = true
    mainRoleId.value = 3
  } else if (row.mainRoleName === '普通用户') {
    roleTableData.value[3].checked = true
    mainRoleId.value = 4
  }

  roleDialogVisible.value = true
}

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确认要删除用户[${row.oaName}]？`, '提示', {
    confirmButtonText: '是',
    cancelButtonText: '否',
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
  })
}

// 角色选择变更
const handleRoleCheck = (role: any) => {
  if (!role.checked && mainRoleId.value === role.id) {
    mainRoleId.value = null
  }
}

// 主角色变更
const handleMainRoleChange = () => {
  // 主角色变更逻辑
}

// 提交表单
const handleSubmit = () => {
  if (!formData.oaAccount) {
    ElMessage.warning('请输入OA账号')
    return
  }

  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('保存成功')
      dialogVisible.value = false
    }
  })
}

// 提交角色
const handleRoleSubmit = () => {
  const checkedRoles = roleTableData.value.filter(role => role.checked)
  if (checkedRoles.length === 0) {
    ElMessage.warning('请至少选择一个角色')
    return
  }

  if (!mainRoleId.value) {
    ElMessage.warning('请设置主角色')
    return
  }

  ElMessage.success('角色绑定成功')
  roleDialogVisible.value = false
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: null,
    organizationName: '',
    departmentCode: '',
    departmentName: '',
    oaAccount: '',
    oaName: '',
    employeeCode: '',
    phone: '',
    accessPermission: '是',
    dataSource: '每晚批量同步'
  })
}

// 关闭对话框
const handleDialogClose = () => {
  resetForm()
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.query-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.query-form {
  margin: 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.menu-style {
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #f9f9f9;
  padding: 10px 0;
}
</style>