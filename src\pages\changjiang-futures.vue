<template>
  <div class="changjiang-futures-container">
    <!-- 左侧菜单 -->
    <ChangjiangFuturesSide />

    <!-- 主体内容区域 -->
    <div class="content">
      <!-- 如果是根路径，显示欢迎页面 -->
      <div v-if="isWelcomePage" class="welcome-page">
        <el-card class="welcome-card">
          <template #header>
            <div class="card-header">
              <span>长江期货风险管理系统</span>
            </div>
          </template>
          <div class="welcome-content">
            <h2>欢迎使用长江期货风险管理系统</h2>
            <p>请从左侧菜单选择相应的功能模块</p>

            <el-row :gutter="20" class="feature-cards">
              <el-col :span="8">
                <el-card shadow="hover" class="feature-card" @click="navigateTo('/changjiang-futures/risk-dashboard')">
                  <div class="feature-icon">
                    <el-icon size="40">
                      <Document />
                    </el-icon>
                  </div>
                  <h3>风险管理驾驶舱</h3>
                  <p>实时监控风险指标和关键数据</p>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="hover" class="feature-card" @click="navigateTo('/changjiang-futures/product-distribution')">
                  <div class="feature-icon">
                    <el-icon size="40">
                      <Setting />
                    </el-icon>
                  </div>
                  <h3>业务风险管理</h3>
                  <p>管理各类业务风险和资产配置</p>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="hover" class="feature-card" @click="navigateTo('/changjiang-futures/market-making-positions')">
                  <div class="feature-icon">
                    <el-icon size="40">
                      <Menu />
                    </el-icon>
                  </div>
                  <h3>长江产业风险管理</h3>
                  <p>产业链风险管控和业务监督</p>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>

      <!-- 子页面内容区域 -->
      <div v-else class="sub-page-content">
        <RouterView />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Document, Setting, Menu } from '@element-plus/icons-vue'
import ChangjiangFuturesSide from '~/components/layouts/ChangjiangFuturesSide.vue'

const route = useRoute()
const router = useRouter()

// 判断是否是欢迎页面（根路径）
const isWelcomePage = computed(() => {
  return route.path === '/changjiang-futures' || route.path === '/changjiang-futures/'
})

// 导航到指定页面
const navigateTo = (path: string) => {
  router.push(path)
}
</script>

<style scoped>
.changjiang-futures-container {
  display: flex;
  height: 100vh;
}

.content {
  flex: 1;
  padding: 20px;
  background-color: #f5f5f5;
}

.welcome-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.welcome-content {
  text-align: center;
  padding: 40px 20px;
}

.welcome-content h2 {
  color: #409eff;
  margin-bottom: 20px;
}

.welcome-content p {
  color: #666;
  margin-bottom: 40px;
  font-size: 16px;
}

.feature-cards {
  margin-top: 40px;
}

.feature-card {
  text-align: center;
  padding: 20px;
  cursor: pointer;
  transition: transform 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  color: #409eff;
  margin-bottom: 15px;
}

.feature-card h3 {
  color: #333;
  margin-bottom: 10px;
}

.feature-card p {
  color: #666;
  font-size: 14px;
}

.sub-page-content {
  height: 100%;
  width: 100%;
}
</style>
