<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import * as echarts from 'echarts';
import { tooltipV2TriggerProps } from 'element-plus/es/components/tooltip-v2/index.mjs';

const route = useRoute();

// 定义报告数据的接口
interface Report {
  id: number;
  title: string;
  date: string;
  summary: string;
  overview: string;
  reportNo: string;
  manager: string;
  status: string;
  data: Array<{ name: string; value: string; trend: number }>;
  conclusions: string[];
}

// 初始化 report 为 undefined 或空对象，并指定类型
const report = ref<Report | null>(null);

let chartInstance = null;

const initChart = () => {
  const chartDom = document.getElementById('report-chart');
  if (!chartDom) return;

  chartInstance = echarts.init(chartDom);

  const option = {
    title: {

      text: '趋势分析'
    },
    tooltip:  {
      trigger: 'axis'
    },
    // tooltip: {
    //   trigger: 'axis',
    //   formatter: (params) => {
    //     return `${params[0].name}<br/>` +
    //       params.map(p => `${p.marker} ${p.seriesName}: ${p.value}`).join('<br/>');
    //   }
    // },
    grid: {
      top: 60,
      bottom: 40,
      left: 60,
      right: 20
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      axisLine: {
        lineStyle: {
          color: '#ccc'
        }
      },
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: {
      type: 'value',
      name: '数值',
      axisLine: {
        lineStyle: {
          color: '#ccc'
        }
      },
      axisLabel: {
        color: '#666'
      }
    },
    legend: {
      data: ['趋势']
    },
    series: [{
      name: '趋势',
      type: 'line',
      data: [120, 132, 101, 134, 90, 230, 210, 250, 300, 320, 280, 260],
      smooth: true,
      showSymbol: false,
      lineStyle: {
        color: '#1890ff'
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(24, 144, 255, 0.2)' },
          { offset: 1, color: 'rgba(24, 144, 255, 0)' }
        ])
      }
    }]
  };

  chartInstance.setOption(option);
};

// 模拟报告数据
const mockReports: Report[] = [
  {
    id: 1,
    title: '2024年第一季度宏观经济分析',
    summary: '本报告对2024年第一季度国内宏观经济运行情况进行全面分析...',
    date: '2024-04-05',
    overview: '详细概述内容...',
    reportNo: 'RPT20240405',
    manager: '张三',
    status: '完成',
    data: [
      { name: 'GDP增长率', value: '5.8%', trend: 0.6 },
      { name: 'CPI同比', value: '2.3%', trend: -0.2 }
    ],
    conclusions: [
      '经济稳步复苏，消费回暖明显。',
      '制造业投资持续增长，成为新亮点。'
    ]
  }
];

onMounted(async () => {
  // 模拟从路由参数获取报告 ID
  const selectedReport = mockReports[0];
  report.value = selectedReport; // 将报告数据赋值给 report

  initChart();
});

// 操作方法
const downloadReport = () => {
  console.log('下载报告');
};

const editReport = () => {
  console.log('编辑报告');
};

const shareReport = () => {
  console.log('分享报告');
};

const deleteReport = () => {
  console.log('删除报告');
};
</script>

<template>
  <div class="report-detail">
    <el-card shadow="never">
      <template #header>
        <div class="flex justify-between items-center">
          <!-- 使用可选链操作符避免访问未定义属性 -->
          <h2 class="text-xl font-bold">{{ report?.title || '加载中...' }}</h2>
          <el-button size="small" type="primary" @click="downloadReport">
            <i class="fa fa-download mr-1"></i>下载报告
          </el-button>
        </div>
      </template>

      <div class="flex flex-col md:flex-row gap-6 mt-4">
        <!-- 左侧内容区 -->
        <div class="md:w-3/4">
          <!-- 报告概述 -->
          <div class="text-left leading-relaxed mt-4">
            <h3 class="text-lg font-semibold mb-2">报告概述</h3>
            <p>{{ report?.overview || '暂无概述' }}</p>
          </div>

          <el-divider />

          <!-- 详细数据 -->
          <div class="mt-4">
            <h3 class="text-lg font-semibold mb-2">详细数据</h3>
            <el-table :data="report?.data || []" stripe>
              <el-table-column prop="name" label="指标名称"></el-table-column>
              <el-table-column prop="value" label="指标值"></el-table-column>
              <el-table-column prop="trend" label="趋势">
                <template #default="scope">
                  <span :class="scope.row.trend > 0 ? 'text-green-500' : 'text-red-500'">
                    {{ scope.row.trend > 0 ? '↑' : '↓' }}{{ Math.abs(scope.row.trend) }}%
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <el-divider />

          <!-- 图表分析 -->
          <div class="mt-4">
            <h3 class="text-lg font-semibold mb-2">图表分析</h3>
            <div id="report-chart" class="chart-container h-80 mt-4"></div>
          </div>

          <el-divider />

          <!-- 结论与建议 -->
          <div class="mt-4">
            <h3 class="text-lg font-semibold mb-2">结论与建议</h3>
            <div class="text-left leading-relaxed mt-4">
              <p v-for="(item, index) in report?.conclusions || []" :key="index" class="mb-3">
                <span class="font-semibold text-primary">{{ index + 1 }}. </span>{{ item }}
              </p>
            </div>
          </div>
        </div>

        <!-- 右侧信息区 -->
        <div class="md:w-1/4">
          <!-- 报告信息 -->
          <el-card title="报告信息" shadow="hover">
            <el-descriptions border direction="vertical">
              <el-descriptions-item label="报告编号">{{ report?.reportNo || '暂无编号' }}</el-descriptions-item>
              <el-descriptions-item label="创建日期">{{ report?.date || '暂无日期' }}</el-descriptions-item>
              <el-descriptions-item label="负责人">{{ report?.manager || '暂无负责人' }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="report?.status === '完成' ? 'success' : 'warning'">
                  {{ report?.status || '未知状态' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>

          <!-- 相关操作 -->
          <el-card title="相关操作" shadow="hover" class="mt-4">
            <el-button type="primary" size="small" block @click="editReport">
              <i class="fa fa-edit mr-1"></i>编辑报告
            </el-button>
            <el-button type="info" size="small" block class="mt-2" @click="shareReport">
              <i class="fa fa-share-alt mr-1"></i>分享报告
            </el-button>
            <el-button type="danger" size="small" block class="mt-2" @click="deleteReport">
              <i class="fa fa-trash mr-1"></i>删除报告
            </el-button>
          </el-card>
        </div>
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.report-detail {
  padding: 0 16px;
}

.chart-container {
  border: 1px solid var(--ep-border-color);
  border-radius: 4px;
  padding: 16px;
}
</style>