<template>
  <div class="dashboard-container">
    <!-- 研报列表区域 -->
    <el-card class="report-card" shadow="never">
       <template #header>
        <div class="card-header">
          <span class="card-title">最新研报</span>
          <el-button type="text" @click="loadMoreReports">更多。。。</el-button>
        </div>
      </template>
      <div class="filter-bar">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleDateChange"
            />
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="selectedCategory"
              placeholder="报告类型"
              clearable
              @change="handleFilterChange"
            >
              <el-option
                v-for="item in categories"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="selectedStatus"
              placeholder="状态筛选"
              clearable
              @change="handleFilterChange"
            >
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索报告标题/内容"
              clearable
              @input="handleSearch"
            >
              <template #append>
                <el-button :icon="Search" />
              </template>
            </el-input>
          </el-col>
        </el-row>
      </div>

      <!-- 报告列表 -->
      <div class="report-list">
        <template v-if="reportList.length > 0">
          <el-row :gutter="20">
            <el-col
              v-for="report in reportList"
              :key="report.id"
              :xs="24"
              :sm="12"
              :md="8"
              :lg="6"
            >
              <ResearchReportItem
                :report="report"
                @click="handleReportClick(report)"
                @download="handleDownload"
                @favorite="handleFavorite"
              />
            </el-col>
          </el-row>
        </template>
        <el-empty v-else description="暂无报告数据" />
      </div>

      <!-- 更多链接 -->
      <!-- <div class="pagination" style="display: flex; justify-content: flex-end;">
        <el-button        
          type="text"
          @click="loadMoreReports"
        >
          更多。。。
        </el-button>
      </div> -->
    </el-card>

    <br />

    <!-- 图表区域 -->
    <el-card class="report-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">最新数据</span>
          <el-button type="text" @click="loadMoreReports">更多。。。</el-button>
        </div>
      </template>
      <div class="chart-container">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-row :gutter="24">
              <el-col
                v-for="(chart, index) in charts"
                :key="index"
                :span="6"
                class="chart-item"
              >
                <el-card shadow="hover" class="chart-card">
                  <template #header>
                    <div class="card-header">
                      <span>{{ chart.title }}</span>
                      <el-button type="text" @click.stop="openFullScreenChart(chart)">
                        <el-icon><FullScreen /></el-icon>
                      </el-button>
                    </div>
                  </template>
                  <div :id="'chart-' + index" class="echart" style="width: 100%; height: 250px;"></div>
                </el-card>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 全屏图表弹窗 -->
    <el-dialog v-model="isDialogVisible" title="详细图表" width="80%">
      <div id="full-chart" class="full-chart" style="width: 100%; height: 600px;"></div>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  FullScreen,
  Postcard,
  Notebook,
  Files,
  Search
} from '@element-plus/icons-vue'
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import ResearchReportItem from '~/components/research/ResearchReportItem.vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router';

const router = useRouter();



const showCharts = ref(true)
const charts = ref([])

// 获取最近8年
function getLastNYears(n) {
  const years = []
  const currentYear = new Date().getFullYear()
  for (let i = n - 1; i >= 0; i--) {
    years.push(currentYear - i)
  }
  return years
}

const chartLineColors = ['#1890ff', '#33ccff']
const chartLineGradientColors = ['rgba(24, 144, 255, 0.2)', 'rgba(24, 144, 255, 0)', 'rgba(51, 204, 255, 0.2)', 'rgba(51, 204, 255, 0)']

const years = getLastNYears(2)

for (let i = 0; i < 4; i++) {
  const seriesData = []

  years.forEach((year, yearIndex) => {
    const data = []
    for (let month = 0; month < 12; month++) {
      const value = Math.floor(Math.random() * 500) + 100
      data.push(value)
    }

    seriesData.push({
      name: `${year}年`,
      type: 'line',
      data: data,
      smooth: true,
      showSymbol: false,
      lineStyle: {
        color: chartLineColors[yearIndex % chartLineColors.length] // 使用 yearIndex 控制颜色
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(
          0, 0, 0, 1,
          [
            { offset: 0, color: chartLineGradientColors[(yearIndex * 2) % chartLineGradientColors.length] },
            { offset: 1, color: chartLineGradientColors[(yearIndex * 2 + 1) % chartLineGradientColors.length] }
          ]
        )
      }
    })
  })

  charts.value.push({
    title: `库存-${i + 1}`,
    series: seriesData
  })
}

const loadCharts = () => {
  showCharts.value = true

  setTimeout(() => {
    charts.value.forEach((_, index) => {
      const chartDom = document.getElementById('chart-' + index)
      if (!chartDom) {
        console.error(`未找到图表容器: chart-${index}`)
        return
      }

      const myChart = echarts.init(chartDom)

      // 显示加载动画
      myChart.showLoading({
        text: '加载中...',
        color: '#409EFF',
        textColor: '#666',
        maskColor: 'rgba(255, 255, 255, 0.8)'
      })

      // 构建配置项
      const option = {
        // title: {
        //   text: _.title,
        //   left: 'center'
        // },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let result = params[0].name + '<br/>'
            params.forEach(p => {
              result += `${p.marker} ${p.seriesName}: ${p.value}<br/>`
            })
            return result
          }
        },
        grid: {
          top: 80,
          bottom: 40,
          left: 60,
          right: 20
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          axisLine: {
            lineStyle: {
              color: '#ccc' // x轴轴线颜色
            }
          },
          axisTick: {
            show: false // 隐藏刻度
          },
          axisLabel: {
            color: '#666' // 刻度标签颜色
          }
        },
        yAxis: {
          type: 'value',
          name: '库存量',
          axisLine: {
            lineStyle: {
              color: '#ccc' // y轴轴线颜色
            }
          },
          axisTick: {
            show: false // 隐藏刻度
          },
          axisLabel: {
            color: '#666' // 刻度标签颜色
          }
        },
        legend: {
          data: _.series.map(s => s.name),
          top: 30,
          left: 'center',
          bottom: 10
        },
        series: _.series
      }

      // 设置配置并隐藏 loading
      setTimeout(() => {
        myChart.hideLoading()
        myChart.setOption(option)
      }, 800)
    })
  }, 0)
}

onMounted(() => {
  loadCharts()
})

// 弹窗相关逻辑
const isDialogVisible = ref(false)
let fullChart = null

const openFullScreenChart = (chartData) => {
  isDialogVisible.value = true;

  nextTick(() => {
    if (!fullChart) {
      fullChart = echarts.init(document.getElementById('full-chart'));
    }

    const option = {
      title: {
        text: chartData.title, // 注意这里修正为 chartData.title
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          let result = params[0].name + '<br/>';
          params.forEach((p) => {
            result += `${p.marker} ${p.seriesName}: ${p.value}<br/>`;
          });
          return result;
        }
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        name: '月份'
      },
      yAxis: {
        type: 'value',
        name: '库存量',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#666'
          }
        }
      },
      legend: {
        data: chartData.series.map((s) => s.name),
        top: 30,
        left: 'center',
        bottom: 10
      },
      series: chartData.series
    };

    fullChart.setOption(option);
  });
};

// ================== 研报相关数据 ==================
// 筛选条件
const dateRange = ref([])
const selectedCategory = ref('')
const selectedStatus = ref('')
const searchKeyword = ref('')

// 模拟数据选项
const categories = [
  { value: 'macro', label: '宏观报告' },
  { value: 'commodity', label: '商品期货' },
  { value: 'financial', label: '金融期货' },
  { value: 'strategy', label: '投资策略' }
]

const statusOptions = [
  { value: 'draft', label: '草稿' },
  { value: 'published', label: '已发布' },
  { value: 'archived', label: '已归档' }
]

// 测试数据
const mockReports = [
  {
    id: 1,
    title: '2024年第一季度宏观经济分析',
    category: 'macro',
    summary: '本报告对2024年第一季度国内宏观经济运行情况进行全面分析...',
    publishDate: '2024-04-05',
    author: '张三',
    status: 'published',
    downloadCount: 120,
    favoriteCount: 35
  },
  {
    id: 2,
    title: '铜期货市场走势预测报告',
    category: 'commodity',
    summary: '通过对供需关系和技术面的综合分析，预测未来三个月铜价走势...',
    publishDate: '2024-04-04',
    author: '李四',
    status: 'published',
    downloadCount: 89,
    favoriteCount: 27
  },
  {
    id: 3,
    title: '股指期货投资策略建议',
    category: 'financial',
    summary: '基于当前经济形势与政策变化，提出二季度股指期货操作建议是需要...',
    publishDate: '2024-04-03',
    author: '王五',
    status: 'draft',
    downloadCount: 0,
    favoriteCount: 10
  },
  {
    id: 4,
    title: '农产品期货套利机会分析',
    category: 'commodity',
    summary: '分析玉米、大豆等主要农产品之间的跨品种套利机会及风险控制...',
    publishDate: '2024-04-02',
    author: '赵六',
    status: 'published',
    downloadCount: 67,
    favoriteCount: 18
  }
]

const reportListData = ref(mockReports)
const totalReports = ref(reportListData.value.length)
const reportList = ref(reportListData.value.slice(0, 10))

// 当前页码和每页显示数量
const currentPage = ref(1)
const pageSize = ref(10)

// 加载更多数据
function loadMoreReports() {
   ElMessage.info('已加载全部数据')
}

function loadReports() {
  const keyword = searchKeyword.value.toLowerCase()
  const category = selectedCategory.value
  const status = selectedStatus.value

  let filtered = reportListData.value.filter((report) => {
    return (
      (!category || report.category === category) &&
      (!status || report.status === status) &&
      (!keyword ||
        report.title.toLowerCase().includes(keyword) ||
        report.summary.toLowerCase().includes(keyword))
    )
  })

  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value

  // 追加新数据到现有列表
  if (currentPage.value > 1) {
    reportList.value = [...reportList.value, ...filtered.slice(start, end)]
  } else {
    reportList.value = filtered.slice(start, end)
  }

  totalReports.value = filtered.length
}

function handleFilterChange() {
  currentPage.value = 1
  loadReports()
}

function handleDateChange() {
  handleFilterChange()
}

function handleSearch() {
  handleFilterChange()
}

function handleSizeChange(val) {
  pageSize.value = val
  loadReports()
}

function handleCurrentChange(val) {
  currentPage.value = val
  loadReports()
}

function handleReportClick(report) {
  console.log('打开报告详情:', report)
  router.push({
    // path: `/report-detail/${report.id}`
    path: `/report-detail`
  });
}

function handleDownload(report) {
  console.log('下载报告:', report)
}

function handleFavorite(report) {
  console.log('收藏报告:', report)
}
</script>

<style scoped lang="scss">
.card-title {
  font-size: 24px; 
  font-weight: bold; 
}

.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
}

.inventory-chart {
  padding: 20px;
}

.chart-card {
  margin-bottom: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.menu-style {
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #f9f9f9;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.report-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.filter-bar {
  margin-bottom: 20px;
}

.report-list {
  margin-bottom: 20px;
}

.pagination {
  display: flex;
  justify-content: center;
  font-size: 14px;
}

.pagination :deep(.el-pager li) {
  background-color: transparent !important;
  color: #606266;
}

.pagination :deep(.el-pager li.active) {
  color: var(--el-color-primary);
  font-weight: bold;
}
</style>