<template>
  <div class="redirect-container">
    <el-card class="redirect-card">
      <div class="redirect-content">
        <el-icon class="redirect-icon" size="60">
          <Loading />
        </el-icon>
        <h2>正在跳转到长江期货...</h2>
        <p>如果页面没有自动跳转，请点击下方按钮</p>
        <el-button type="primary" @click="redirectToChangjiangFutures">
          进入长江期货
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Loading } from '@element-plus/icons-vue'
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const redirectToChangjiangFutures = () => {
  router.push('/changjiang-futures')
}

onMounted(() => {
  // 自动跳转到长江期货页面
  setTimeout(() => {
    redirectToChangjiangFutures()
  }, 2000)
})
</script>

<style scoped>
.redirect-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.redirect-card {
  width: 400px;
  text-align: center;
}

.redirect-content {
  padding: 40px 20px;
}

.redirect-icon {
  color: #409eff;
  margin-bottom: 20px;
}

.redirect-content h2 {
  color: #333;
  margin-bottom: 15px;
}

.redirect-content p {
  color: #666;
  margin-bottom: 30px;
}
</style>