<template>
  <el-dialog
    v-model="isUploadDialogVisible"
    title="上传指标"
    width="600px"
    @close="hideUploadDialog"
  >
    <!-- 指标名称 -->
    <div class="input-row">
      <label>指标名称：测试指标 </label>
      <el-button size="small" @click="downloadTemplate">下载模板</el-button>
    </div>

    <!-- 拖拽区域 -->
    <el-upload
      class="drop-zone"
      :class="{ 'drag-over': isFileDragging }"
      drag
      :show-file-list="true"
      :auto-upload="false"
      :on-change="handleFileSelection"
      :on-drop="handleFileDrop"
      :on-drag-enter="handleFileDragEnter"
      :on-drag-leave="handleFileDragLeave"
    >
      <p>将文件拖放到此处 或 点击选择文件</p>
      <input type="file" style="display: none" ref="fileInputRef" />
    </el-upload>

    <!-- 描述 -->
    <div class="description-text">
      <p>支持格式：.xlsx，最大不超过10MB</p>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <el-button @click="hideUploadDialog">取消</el-button>
      <el-button type="primary" @click="submitFileUpload">确定</el-button>
    </template>
  </el-dialog>
  <div class="report-entry-container">
    <el-row :gutter="20">
      <!-- 左侧菜单 -->
      <el-col :span="3">
        <el-menu
          :default-active="activeMenuItemId"
          @select="handleMenuItemSelect"
          class="menu-style"
        >
          <el-menu-item v-for="(menuItem, index) in menuItems" :key="index" :index="menuItem.id.toString()">
            <el-icon><component :is="menuItem.icon" /></el-icon>
            <span style="margin-left: 10px;">{{ menuItem.label }}</span>
          </el-menu-item>
        </el-menu>
      </el-col>

      <!-- 右侧主内容 -->
      <el-col :span="21">
        <!-- 查询区域卡片 -->
        <el-card class="table-query">        
          <el-form :inline="true" :model="searchQuery" label-width="80px" class="search-form">
            <el-form-item label="指标编码">
              <el-input v-model="searchQuery.indexCode" placeholder="请输入指标编码" />
            </el-form-item>
            <el-form-item label="指标名称">
              <el-input v-model="searchQuery.indexNam" placeholder="请输入指标名称" />
            </el-form-item>

            <el-form-item label="开始日期">
              <el-date-picker
                v-model="searchQuery.startDate"
                type="date"
                placeholder="选择开始日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>

            <el-form-item label="结束日期">
              <el-date-picker
                v-model="searchQuery.endDate"
                type="date"
                placeholder="选择结束日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>

            <el-form-item>
              <el-button plain type="primary" @click="fetchIndicatorData(true)">查询</el-button>
              <el-button plain type="warning" @click="showUploadDialog">数据上传</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 表格区域卡片 -->
        <el-card class="table-card">         
          <div class="table-container">
            <el-table :data="indicatorTableData" border style="width: 100%" v-loading="isDataLoading">
              <el-table-column prop="dataDate" label="数据日期" width="120" align="center" />
              <el-table-column prop="indexCode" label="指标编码" width="180" align="center" />
              <el-table-column prop="indexNam" label="指标名称" width="200" align="center" />
              <el-table-column prop="indexValue" label="指标值" align="center" />
              <el-table-column prop="indexValueRate" label="环比" align="center" />
              <el-table-column prop="createDate" label="创建时间" width="120" align="center" />

              <!-- 可选操作列 -->
              <el-table-column label="操作" width="150" align="center">
                <template #default="{ $index }">
                  <el-button size="small" @click="editIndicatorData($index)">编辑</el-button>
                  <el-button size="small" type="danger" @click="deleteIndicatorData($index)">删除</el-button>
                </template>
              </el-table-column>
              <template #empty>
                <span>暂无数据</span>
              </template>
            </el-table>
          </div>
          <div class="pagination-container">
            <el-pagination
              layout="prev, pager, next, sizes, total"
              :total="totalIndicatorCount"
              v-model:current-page="currentPageNumber"
              v-model:page-size="pageSizeNumber"
              @size-change="fetchIndicatorData"
              @current-change="fetchIndicatorData"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import http from '~//utils/http'

import {
  Postcard,
  DocumentAdd
} from '@element-plus/icons-vue'

// 上传对话框相关
const isUploadDialogVisible = ref(false)
const isFileDragging = ref(false)
const selectedFile = ref(null)
const fileInputRef = ref(null)

// 菜单相关
const activeMenuItemId = ref('1')
const menuItems = ref([
  { id: 1, label: '系统采购数据查看', icon: Postcard },
  { id: 2, label: '人工编辑数据上传', icon: DocumentAdd }
])

// 分页相关
const totalIndicatorCount = ref(0)
const currentPageNumber = ref(1)
const pageSizeNumber = ref(10)
const isDataLoading = ref(false)

// 表格数据
const indicatorTableData = ref<any[]>([])

// 查询条件
const searchQuery = ref({
  indexCode: '',
  indexNam: '',
  startDate: '',
  endDate: ''
})

// 菜单选择事件处理
const handleMenuItemSelect = (index: string) => {
  console.log('选中的菜单项:', index)
}

// 加载数据
const fetchIndicatorData = async (newPageNumOrReset?: number | boolean) => {
  let resetPage = false;

  if (typeof newPageNumOrReset === 'boolean') {
    resetPage = newPageNumOrReset;
  } else if (typeof newPageNumOrReset === 'number') {
    currentPageNumber.value = newPageNumOrReset;
  }

  isDataLoading.value = true;
  try {
    const params = {
      pageNum: currentPageNumber.value,
      pageSize: pageSizeNumber.value,
      ...searchQuery.value
    };

    const res = await http.get('/futuresMarketData/list', { params });
    indicatorTableData.value = res.data.records || [];
    totalIndicatorCount.value = res.data.total;

    ElMessage.success('查询成功');
  } catch (error) {
    ElMessage.error('查询失败');
    console.error('获取数据失败:', error);
  } finally {
    isDataLoading.value = false;
  }
};

// 显示上传对话框
const showUploadDialog = () => {
  isUploadDialogVisible.value = true
}

// 隐藏上传对话框
const hideUploadDialog = () => {
  isUploadDialogVisible.value = false
}

// 编辑数据
const editIndicatorData = (index: number) => {
  alert(`编辑第 ${index + 1} 行`)
}

// 删除数据
const deleteIndicatorData = (index: number) => {
  indicatorTableData.value.splice(index, 1)
}

// 拖拽进入
const handleFileDragEnter = () => {
  isFileDragging.value = true
}

// 拖拽离开
const handleFileDragLeave = () => {
  isFileDragging.value = false
}

// 拖拽放置
const handleFileDrop = (event: DragEvent) => {
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    selectedFile.value = files[0]
    console.log('已选文件:', selectedFile.value.name)
  }
  isFileDragging.value = false
}

// 文件选择
// const handleFileSelection = (event: Event) => {
//   const files = (event.target as HTMLInputElement).files
//   if (files && files.length > 0) {
//     selectedFile.value = files[0]
//     console.log('已选文件:', selectedFile.value.name)
//   }
// }
const handleFileSelection = (uploadFile: any, uploadFiles: any) => {
  // 使用 raw 属性获取原生 File 对象
  const file = uploadFile.raw;
  if (file) {
    selectedFile.value = file;
    console.log('已选文件:', file.name);
  }
};

// 提交文件上传
const submitFileUpload = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请选择要上传的文件');
    return;
  }

  const file = selectedFile.value;

  // 创建 FormData 对象并附加文件
  const formData = new FormData();
  formData.append('file', file); // 后端接收字段名为 'file'

  try {
    // 发送 POST 请求到后端接口
    const res = await http.post('/futuresMarketData/importExcel', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    // 根据响应处理成功逻辑
    if (res.code === 200) { // 假设后端返回 code 为 200 表示成功
      ElMessage.success('文件上传成功');
      fetchIndicatorData(); // 刷新表格数据
      hideUploadDialog();
    } else {
      ElMessage.error(res.message || '文件上传失败');
    }
  } catch (error) {
    console.error('上传失败:', error);
    ElMessage.error('上传过程中发生错误');
  }
};

// 下载模板
const downloadTemplate = () => {
  const link = document.createElement('a')
  link.href = 'https://*************/investment-api/futuresMarketData/downloadTemplate'
  link.download = '指标上传模板.xlsx'
  link.click()
}

// 初始化数据
onMounted(() => {
  fetchIndicatorData()
})
</script>

<style scoped lang="scss">
.report-entry-container {
  padding: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.menu-style {
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #f9f9f9;
  padding: 10px 0;
  height: fit-content;
}

.table-query {
  margin-bottom: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.table-card {
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-header {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.search-form {
  padding: 10px 0;
}

.table-container {
  margin-bottom: 20px;
}

.pagination-container {
  text-align: center;
}

.input-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.drop-zone {
  border: 2px dashed #ccc;
  padding: 20px;
  text-align: center;
  margin-bottom: 15px;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.drop-zone.drag-over {
  border-color: blue;
  background-color: #f0f8ff;
}

.description-text {
  text-align: left;
  font-size: 13px;
  color: #666;
  margin-bottom: 20px;
}
</style>
