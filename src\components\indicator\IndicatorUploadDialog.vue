<template>
  <el-dialog
    v-model="dialogVisible"
    title="上传指标"
    width="500px"
    :before-close="handleClose"
    center
  >
    <!-- 指标名称 -->
    <div class="input-row">
      <label>指标名称：{{ indicatorName }}</label>
      <el-button size="small" @click="downloadTemplate">下载模板</el-button>
    </div>

    <!-- 拖拽区域 -->
    <div
      class="drop-zone"
      :class="{ 'drag-over': isDragging }"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      @drop="handleDrop"
      @dragover.prevent
    >
      <p>将文件拖放到此处 或 点击选择文件</p>
      <input type="file" style="display: none" @change="handleFileChange" ref="fileInput" />
    </div>

    <!-- 描述 -->
    <div class="description-text">
      <p>支持格式：.xlsx，最大不超过10MB</p>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" @click="submitUpload">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { Close } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  indicatorName: {
    type: String,
    default: '测试指标'
  }
})

const emit = defineEmits(['update:modelValue', 'upload'])

// 响应式状态
const dialogVisible = ref(false)
const isDragging = ref(false)
const selectedFile = ref(null)
const fileInput = ref(null)

// 同步外部 modelValue 到内部状态
watch(() => props.modelValue, (newVal) => {
  dialogVisible.value = newVal
})

// 关闭对话框（通过 el-dialog 的 before-close）
const handleClose = (done) => {
  closeDialog()
}

// 关闭弹窗并通知父组件
const closeDialog = () => {
  emit('update:modelValue', false)
}

// 拖拽进入
const handleDragEnter = () => {
  isDragging.value = true
}

// 拖拽离开
const handleDragLeave = () => {
  isDragging.value = false
}

// 拖拽放置
const handleDrop = (event) => {
  const files = event.dataTransfer.files
  if (files.length > 0) {
    selectedFile.value = files[0]
    console.log('已选文件:', selectedFile.value.name)
  }
  isDragging.value = false
}

// 点击上传按钮
const submitUpload = () => {
  if (!selectedFile.value) {
    ElMessage.warning('请选择要上传的文件')
    return
  }

  // 触发上传事件
  emit('upload', {
    indicatorName: props.indicatorName,
    file: selectedFile.value
  })

  ElMessage.success('上传成功（演示）')
  closeDialog()
}

// 文件选择
const handleFileChange = (event) => {
  const files = event.target.files
  if (files.length > 0) {
    selectedFile.value = files[0]
    console.log('已选文件:', selectedFile.value.name)
  }
}

// 打开文件选择器
const openFilePicker = () => {
  fileInput.value.click()
}

// 下载模板
const downloadTemplate = () => {
  const link = document.createElement('a')
  link.href = '/static/template/indicator_template.xlsx'
  link.download = '指标上传模板.xlsx'
  link.click()
}
</script>

<style scoped>
.input-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.drop-zone {
  border: 2px dashed #ccc;
  padding: 20px;
  text-align: center;
  margin-bottom: 15px;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.drop-zone.drag-over {
  border-color: blue;
  background-color: #f0f8ff;
}

.description-text {
  text-align: left;
  font-size: 13px;
  color: #666;
  margin-bottom: 20px;
}
</style>