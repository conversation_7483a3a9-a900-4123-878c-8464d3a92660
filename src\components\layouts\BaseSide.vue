<script lang="ts" setup>
import {
  Document,
  Menu as IconMenu,
  Location,
  Setting,
} from '@element-plus/icons-vue'

// const isCollapse = ref(true)
function handleOpen(key: string, keyPath: string[]) {
  // eslint-disable-next-line no-console
  console.log(key, keyPath)
}
function handleClose(key: string, keyPath: string[]) {
  // eslint-disable-next-line no-console
  console.log(key, keyPath)
}
</script>

<template>
  <el-menu
    router
    default-active="1"
    class="el-menu-vertical-demo"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-menu-item index="/nav/2">
      <el-icon>
        <IconMenu />
      </el-icon>
      <template #title>
        价格
      </template>
    </el-menu-item>
    <el-menu-item index="/nava/3">
      <el-icon>
        <Document />
      </el-icon>
      <template #title>
        供应
      </template>
    </el-menu-item>
    <el-menu-item index="/nav/4">
      <el-icon>
        <Setting />
      </el-icon>
      <template #title>
        需求
      </template>
    </el-menu-item>
    <el-menu-item index="/nav/5">
      <el-icon>
        <Setting />
      </el-icon>
      <template #title>
        库存
      </template>
    </el-menu-item>
    <el-menu-item index="/nav/6">
      <el-icon>
        <Setting />
      </el-icon>
      <template #title>
        成本利润
      </template>
    </el-menu-item>
    <el-menu-item index="/nav/7">
      <el-icon>
        <Setting />
      </el-icon>
      <template #title>
        进出口
      </template>
    </el-menu-item>
    <el-menu-item index="/nav/8">
      <el-icon>
        <Setting />
      </el-icon>
      <template #title>
        成交持仓
      </template>
    </el-menu-item>
  </el-menu>
</template>
