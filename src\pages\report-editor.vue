<template>
  <div class="report-entry-container">
    <el-row :gutter="20">
      <!-- 左侧菜单 -->
      <el-col :span="2">
        <el-menu
          :default-active="activeMenu"
          :default-openeds="['2']"
          @select="handleMenuSelect"
          class="menu-style"
          unique-opened
        >
          <!-- 遍历一级菜单 -->
          <template v-for="item in menuItems" :key="item.id">
            <!-- 如果没有子菜单，则直接显示一级菜单项 -->
            <el-menu-item v-if="!item.children" :index="item.id">
              <span>{{ item.label }}</span>
            </el-menu-item>

            <!-- 如果有子菜单，则显示为 el-sub-menu -->
            <el-sub-menu v-else :index="item.id">
              <template #title>
                <span>{{ item.label }}</span>
              </template>

              <!-- 遍历二级菜单 -->
              <el-menu-item
                v-for="child in item.children"
                :key="child.id"
                :index="child.id"
              >
                <span style="margin-left: 10px;">{{ child.label }}</span>
              </el-menu-item>
            </el-sub-menu>
          </template>
        </el-menu>
      </el-col>

      <!-- 右侧主内容 -->
      <el-col :span="22">
        <div class="card content-box">
          
          <WangEditor v-model:value="content" height="400px" />
          <el-button type="primary" @click="dialogVisible = true">内容预览</el-button>

          <el-dialog v-model="dialogVisible" title="富文本内容预览" width="1300px" top="50px">
            <div class="view" v-html="content"></div>
          </el-dialog>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts" name="wangEditor">
import { ref } from "vue";
import WangEditor from "~/components/WangEditor.vue";
import { useRouter } from 'vue-router';

const router = useRouter();

// 定义 activeMenu 变量，默认激活菜单项为 '2-2'
const activeMenu = ref('2-2');

// 菜单数据
const menuItems = ref([
  {
    id: '1',
    label: '研报列表'
  },
  {
    id: '2',
    label: '研报管理',
    children: [
      { id: '2-1', label: '上传研报' },
      { id: '2-2', label: '系统制作研报' }
    ]
  }
]);

// 菜单选择事件处理
const handleMenuSelect = (index: string) => {
  console.log('点击了菜单项:', index);

  if (index === '1') {
    // 跳转到研报列表页面
    router.push('/report');
  } else if (index === '2-1') {
    // 跳转到上传研报页面
    router.push('/report-manage');
  } else if (index === '2-2') {
    // 跳转到系统制作研报页面
    router.push('/report-editor'); 
  }
};

// 富文本内容
const content = ref("");

// 对话框可见性
const dialogVisible = ref(false);
</script>

<style scoped lang="scss">
/* 富文本组件校验失败样式 */
.is-error {
  .editor-box {
    border-color: red; /* 校验失败时显示红色边框 */
    .editor-toolbar {
      border-bottom-color: red;
    }
  }
}

/* 富文本组件禁用样式 */
.editor-disabled {
  cursor: not-allowed !important;
  .editor-box {
    border-color: #ccc !important; /* 禁用状态下显示灰色边框 */
    .editor-toolbar {
      border-bottom-color: #ccc !important;
    }
  }
}

/* 富文本组件样式 */
.editor-box {
  /* 防止富文本编辑器全屏时 tabs组件 在其层级之上 */
  z-index: 2;
  width: 95%;
  border: 2px solid rgb(194, 194, 194); /* 设置黑色边框 */
  border-radius: 4px; /* 添加圆角 */
  transition: none; /* 移除过渡效果 */

  .editor-toolbar {
    border-bottom: 2px solid rgb(194, 194, 194); /* 工具栏下边框 */
    background-color: #f9f9f9; /* 工具栏背景色 */
  }

  .editor-content {
    overflow-y: auto; /* 确保内容区域可滚动 */
    min-height: 200px; /* 设置最小高度 */
    padding: 10px; /* 内边距 */
    border-top: none; /* 避免重复边框 */
  }
}
</style>