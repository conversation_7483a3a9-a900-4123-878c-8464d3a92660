<template>
  <el-menu class="el-menu-demo" mode="horizontal" :ellipsis="false" router>    
    <el-sub-menu
        v-for="item in menuData"
        :key="item.index"
        :index="item.index"
        :popper-options="{
            placement: 'bottom-start',
            modifiers: [{ name: 'preventOverflow', options: { padding: 16 } }]
        }"
        >
        <template #title>{{ item.title }}</template>
        <el-scrollbar max-height="300px">
            <el-menu-item
            v-for="child in item.children"
            :key="child.index"
            :index="child.index"
            >
            {{ child.label }}
            </el-menu-item>
        </el-scrollbar>
    </el-sub-menu>
  </el-menu>
</template>
<script lang="ts" setup>

import { ref } from 'vue'

// const menuData = ref([
//   {
//     index: '1',
//     title: '能源化工',
//     children: [
//       { index: '1-1', label: '原油' },
//       { index: '1-2', label: 'LPG' },
//       { index: '1-3', label: '沥青' },
//       { index: '1-4', label: '甲醇' },
//       { index: '1-5', label: '聚乙烯' },
//       { index: '1-6', label: '聚丙烯' },
//       { index: '1-7', label: '聚氯乙烯' },
//       { index: '1-8', label: '烧碱' },
//       { index: '1-9', label: '苯乙烯' },
//       { index: '1-10', label: 'PTA' },
//       { index: '1-11', label: '乙二醇' },
//       { index: '1-12', label: '玻璃' },
//       { index: '1-13', label: '纯碱' },
//       { index: '1-14', label: '橡胶' },
//       { index: '1-15', label: '纸浆' },
//       { index: '1-16', label: '尿素' },
//       { index: '1-17', label: 'PX' },
//       { index: '1-18', label: '短纤' },
//       { index: '1-19', label: '集运欧线' },
//       { index: '1-20', label: '丁二烯橡胶' },
//       { index: '1-21', label: '瓶片' }
//     ]
//   },
//   {
//     index: '3',
//     title: '黑色金属',
//     children: [
//       { index: '2-1', label: '钢材' },
//       { index: '2-2', label: '铁矿石' },
//       { index: '2-3', label: '双焦' },
//       { index: '2-4', label: '铁合金' },
//       { index: '2-4', label: '动力煤' }
//     ]
//   },
//   {
//     index: '3',
//     title: '有色金属',
//     children: [
//       { index: '3-1', label: '铜' },
//       { index: '3-2', label: '锌' },
//       { index: '3-3', label: '镍' },
//       { index: '3-4', label: '铝' },
//       { index: '3-5', label: '铅' },
//       { index: '3-6', label: '氧化铝' },      
//       { index: '3-7', label: '不锈钢' }
//     ]
//   },
//   {
//     index: '4',
//     title: '贵金属',
//     children: [
//       { index: '4-1', label: '黄金' },
//       { index: '4-2', label: '白银' }
//     ]
//   },
//   {
//     index: '5',
//     title: '农产品',
//     children: [
//       { index: '5-1', label: '棕榈油' },
//       { index: '5-2', label: '豆油' },
//       { index: '5-3', label: '菜油' },
//       { index: '5-4', label: '豆粕' },
//       { index: '5-5', label: '菜粕' },
//       { index: '5-6', label: '豆一' },
//       { index: '5-7', label: '豆二' },
//       { index: '5-8', label: '油菜籽' },
//       { index: '5-9', label: '玉米' },
//       { index: '5-10', label: '玉米淀粉' },
//       { index: '5-11', label: '花生' },
//       { index: '5-12', label: '白糖' },
//       { index: '5-13', label: '棉花' },
//       { index: '5-14', label: '苹果' },
//       { index: '5-15', label: '生猪' },
//       { index: '5-16', label: '鸡蛋' },
//       { index: '5-17', label: '小麦' },
//       { index: '5-18', label: '红枣' },
//       { index: '5-19', label: '原木' }
//     ]
//   },
//   {
//     index: '6',
//     title: '新能源',
//     children: [
//       { index: '6-1', label: '工业硅' },
//       { index: '6-2', label: '多晶硅' },
//       { index: '6-3', label: '锂' }
//     ]
//   },
//   {
//     index: '7',
//     title: '金融品',
//     children: [
//       { index: '7-1', label: '股指期货' },
//       { index: '7-2', label: '国债期货' }
//     ]
//   },
//   {
//     index: '8',
//     title: '宏观',
//     children: [
//       { index: '8-1', label: '中国' },
//       { index: '8-2', label: '美国' },
//       { index: '8-3', label: '欧盟' },
//       { index: '8-4', label: '德国' },
//       { index: '8-5', label: '法国' },
//       { index: '8-6', label: '英国' },
//       { index: '8-7', label: '加拿大' },
//       { index: '8-8', label: '日本' },
//       { index: '8-9', label: '韩国' },
//       { index: '8-10', label: '俄罗斯' },
//       { index: '8-11', label: '印度' },
//       { index: '8-12', label: '世界银行' },
//       { index: '8-13', label: '国际货币基金组织' },
//       { index: '8-14', label: '经济合作与发展组织' },
//       { index: '8-15', label: '联合国' }
//     ]
//   }
// ])

const menuData = ref([
  {
    index: '1',
    title: '能化',
    children: [
      { index: '1-1', label: '原油' },
      { index: '1-2', label: 'PTA' },
      { index: '1-3', label: 'PVC' },
      { index: '1-4', label: '烧碱' },
      { index: '1-5', label: '尿素' },
      { index: '1-6', label: '纯碱' },
      { index: '1-7', label: '甲醇' }
    ]
  },
  {
    index: '2',
    title: '黑色',
    children: [
      { index: '2-1', label: '螺纹' },
      { index: '2-2', label: '热卷' },
      { index: '2-3', label: '焦煤' },
      { index: '2-4', label: '焦炭' },
      { index: '2-5', label: '铁矿石' },
      { index: '2-6', label: '玻璃' }
    ]
  },
  {
    index: '3',
    title: '有色',
    children: [
      { index: '3-1', label: '铜' },
      { index: '3-2', label: '铝' },
      { index: '3-3', label: '氧化铝' },
      { index: '3-4', label: '碳酸锂' },
      { index: '3-5', label: '工业硅' },
      { index: '3-6', label: '多晶硅' }
    ]
  },
  {
    index: '4',
    title: '农产品',
    children: [
      { index: '4-1', label: '生猪' },
      { index: '4-2', label: '鸡蛋' },
      { index: '4-3', label: '玉米' },
      { index: '4-4', label: '豆粕' },
      { index: '4-5', label: '菜粕' },
      { index: '4-6', label: '豆油' },
      { index: '4-7', label: '菜油' },
      { index: '4-8', label: '棕榈油' },
      { index: '4-9', label: '棉花' },
      { index: '4-10', label: '苹果' },
      { index: '4-11', label: '纸浆' },
      { index: '4-12', label: '红枣' }
    ]
  },
  {
    index: '5',
    title: '金融',
    children: [
      { index: '5-1', label: '股指' },
      { index: '5-2', label: '国债' }
    ]
  }
])
</script>
<style lang="scss" scoped>
.el-menu-demo {
  &.ep-menu--horizontal {
    margin-left: 50px;
  }
}
</style>
