<script lang="ts" setup>
// import { repository } from '~/../package.json'

import { toggleDark } from '~/composables'
import SubHeader from '~/components/layouts/SubHeader.vue'
</script>

<template>
  <el-menu class="el-menu-demo" mode="horizontal" :ellipsis="false" router>
    <el-menu-item index="/">
      <div class="flex items-center justify-center gap-2">
        <!-- <div class="text-xl" i-ep-element-plus /> -->
        <div class="text-xl cjfco-logo" />
        <span class="cjfco-title">长江投研</span>
      </div>
    </el-menu-item>

    <el-menu-item index="/">
      <div class="flex active items-center justify-center gap-2">
        <span>首页</span>
      </div>
    </el-menu-item>
        <el-menu-item index="/datacenter">
      <div class="flex items-center justify-center gap-2">
        <span>研究基础数据管理</span>
      </div>
    </el-menu-item>
    <!-- 
    <el-menu-item index="/research">
      <div class="flex items-center justify-center gap-2">
        <span>品种数据中心</span>
      </div>
    </el-menu-item>
        <el-menu-item index="/product-analysis">
      <div class="flex items-center justify-center gap-2">
        <span>222品种分析</span>
      </div>
    </el-menu-item> -->
    <el-menu-item index="/logical-deduction">
      <div class="flex items-center justify-center gap-2">
        <span>品种研究中心（客户）</span>
      </div>
    </el-menu-item>
     <el-menu-item index="/report-manage">
      <div class="flex items-center justify-center gap-2">
        <span>研报中心（客户）</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/admin/user-management">
      <div class="flex items-center justify-center gap-2">
        <span>后台管理</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/admin/gl-indicator">
      <div class="flex items-center justify-center gap-2">
        <span>指标库</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/admin/chart-editor">
      <div class="flex items-center justify-center gap-2">
        <span>制图制表</span>
      </div>
    </el-menu-item>
    
    <!-- <el-sub-menu index="2">
      <template #title>
        Workspace
      </template>
      <el-menu-item index="2-1">
        item one
      </el-menu-item>
      <el-menu-item index="2-2">
        item two
      </el-menu-item>
      <el-menu-item index="2-3">
        item three
      </el-menu-item>
      <el-sub-menu index="2-4">
        <template #title>
          item four
        </template>
        <el-menu-item index="2-4-1">
          item one
        </el-menu-item>
        <el-menu-item index="2-4-2">
          item two
        </el-menu-item>
        <el-menu-item index="2-4-3">
          item three
        </el-menu-item>
      </el-sub-menu>
    </el-sub-menu>
    <el-menu-item index="3" disabled>
      Info
    </el-menu-item>
    <el-menu-item index="4">
      Orders
    </el-menu-item> -->

    <el-menu-item h="full" @click="toggleDark()">
      <button
        class="w-full cursor-pointer border-none bg-transparent"
        style="height: var(--ep-menu-item-height)"
      >
        <i inline-flex i="dark:ep-moon ep-sunny" />
      </button>
    </el-menu-item>
  </el-menu>
  <!-- <SubHeader/> -->
</template>

<style lang="scss">
.el-menu-demo {
  &.ep-menu--horizontal > .ep-menu-item:nth-child(1) {
    margin-right: 50px;
  }
}

.cjfco-logo {
  width: 152px;
  height: 40px;
  background-image: url('~/assets/images/logo.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.cjfco-title {
  font-size: 24px;
  font-weight: bold;
}
</style>
