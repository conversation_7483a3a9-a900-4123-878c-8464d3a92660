<template>
  <div class="risk-dashboard-container">
    <!-- 左侧菜单 -->
    <ChangjiangFuturesSide />

    <!-- 主体内容区域 -->
    <div class="content">
      <!-- 页面标题和操作栏 -->
      <div class="page-header">
        <div class="header-left">
          <h1 class="page-title">风险管理驾驶舱</h1>
          <div class="update-time">
            <span>最后更新：{{ currentTime }}</span>
          </div>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="refreshData">刷新数据</el-button>
          <el-button type="success">导出报告</el-button>
          <el-button type="warning">设置</el-button>
        </div>
      </div>

      <!-- 风险预警横幅 -->
      <div v-if="hasRiskAlert" class="risk-alert">
        <el-alert
          title="风险预警"
          type="warning"
          :description="riskAlertMessage"
          show-icon
          :closable="false"
        />
      </div>

      <!-- 核心风险指标 -->
      <div class="metrics-section">
        <el-row :gutter="16">
          <el-col :span="4" v-for="(metric, index) in coreMetrics" :key="index">
            <div class="metric-card" :class="metric.status">
              <div class="metric-info">
                <div class="metric-value">{{ metric.value }}</div>
                <div class="metric-label">{{ metric.label }}</div>
                <div class="metric-change" :class="metric.changeType">
                  {{ metric.change }}
                </div>
              </div>
              <div class="metric-progress">
                <el-progress
                  :percentage="metric.percentage"
                  :color="metric.progressColor"
                  :show-text="false"
                  :stroke-width="4"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 风险监控面板 -->
      <el-row :gutter="16" class="monitoring-section">
        <!-- 风险等级分布 -->
        <el-col :span="8">
          <el-card class="chart-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>风险等级分布</span>
                <el-button type="text" size="small">详情</el-button>
              </div>
            </template>
            <div class="risk-level-chart">
              <div class="risk-item" v-for="item in riskLevelData" :key="item.level">
                <div class="risk-level" :class="item.class">{{ item.level }}</div>
                <div class="risk-count">{{ item.count }}个</div>
                <div class="risk-bar">
                  <div class="risk-bar-fill" :style="{ width: item.percentage + '%', backgroundColor: item.color }"></div>
                </div>
                <div class="risk-percentage">{{ item.percentage }}%</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 资金使用情况 -->
        <el-col :span="8">
          <el-card class="chart-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>资金使用情况</span>
                <el-button type="text" size="small">详情</el-button>
              </div>
            </template>
            <div class="fund-usage">
              <div class="fund-circle">
                <el-progress
                  type="circle"
                  :percentage="fundUsagePercentage"
                  :width="120"
                  :stroke-width="8"
                  :color="fundUsageColor"
                >
                  <template #default="{ percentage }">
                    <span class="percentage-value">{{ percentage }}%</span>
                    <span class="percentage-label">使用率</span>
                  </template>
                </el-progress>
              </div>
              <div class="fund-details">
                <div class="fund-item">
                  <span class="fund-label">总资金：</span>
                  <span class="fund-value">¥ 50,000,000</span>
                </div>
                <div class="fund-item">
                  <span class="fund-label">已使用：</span>
                  <span class="fund-value used">¥ 32,500,000</span>
                </div>
                <div class="fund-item">
                  <span class="fund-label">可用余额：</span>
                  <span class="fund-value available">¥ 17,500,000</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 风险趋势 -->
        <el-col :span="8">
          <el-card class="chart-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>风险趋势</span>
                <el-select v-model="trendPeriod" size="small" style="width: 80px;">
                  <el-option label="7天" value="7d" />
                  <el-option label="30天" value="30d" />
                  <el-option label="90天" value="90d" />
                </el-select>
              </div>
            </template>
            <div class="trend-chart">
              <div class="trend-line" v-for="(point, index) in trendData" :key="index">
                <div class="trend-date">{{ point.date }}</div>
                <div class="trend-value" :class="point.status">{{ point.value }}%</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 持仓风险监控 -->
      <el-row :gutter="16" class="position-section">
        <el-col :span="16">
          <el-card class="table-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>持仓风险监控</span>
                <div class="header-actions">
                  <el-input
                    v-model="searchKeyword"
                    placeholder="搜索品种"
                    size="small"
                    style="width: 200px; margin-right: 10px;"
                    clearable
                  >
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>
                  <el-button type="primary" size="small">导出</el-button>
                </div>
              </div>
            </template>
            <el-table :data="filteredRiskData" style="width: 100%" size="small">
              <el-table-column prop="product" label="品种" width="100" />
              <el-table-column prop="contract" label="合约" width="100" />
              <el-table-column prop="position" label="持仓量" width="100" align="right" />
              <el-table-column prop="marketValue" label="市值(万)" width="120" align="right" />
              <el-table-column prop="riskLevel" label="风险等级" width="100" align="center">
                <template #default="scope">
                  <el-tag :type="getRiskLevelType(scope.row.riskLevel)" size="small">
                    {{ scope.row.riskLevel }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="riskValue" label="风险值" width="100" align="right" />
              <el-table-column prop="margin" label="保证金(万)" width="120" align="right" />
              <el-table-column prop="profitLoss" label="浮动盈亏(万)" width="120" align="right">
                <template #default="scope">
                  <span :class="scope.row.profitLoss >= 0 ? 'profit' : 'loss'">
                    {{ scope.row.profitLoss >= 0 ? '+' : '' }}{{ scope.row.profitLoss }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80" align="center">
                <template #default="scope">
                  <el-tag :type="scope.row.status === '正常' ? 'success' : 'warning'" size="small">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template #default="scope">
                  <el-button type="text" size="small" @click="viewDetail(scope.row)">详情</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>

        <!-- 风险预警列表 -->
        <el-col :span="8">
          <el-card class="alert-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>风险预警</span>
                <el-badge :value="alertCount" class="alert-badge">
                  <el-button type="text" size="small">全部</el-button>
                </el-badge>
              </div>
            </template>
            <div class="alert-list">
              <div class="alert-item" v-for="alert in riskAlerts" :key="alert.id">
                <div class="alert-icon" :class="alert.level">
                  <el-icon>
                    <Warning v-if="alert.level === 'high'" />
                    <InfoFilled v-else />
                  </el-icon>
                </div>
                <div class="alert-content">
                  <div class="alert-title">{{ alert.title }}</div>
                  <div class="alert-desc">{{ alert.description }}</div>
                  <div class="alert-time">{{ alert.time }}</div>
                </div>
                <div class="alert-action">
                  <el-button type="text" size="small">处理</el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 市场行情监控 -->
      <el-row :gutter="16" class="market-section">
        <el-col :span="24">
          <el-card class="market-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>重点品种行情监控</span>
                <div class="market-tabs">
                  <el-radio-group v-model="marketCategory" size="small">
                    <el-radio-button label="all">全部</el-radio-button>
                    <el-radio-button label="metal">金属</el-radio-button>
                    <el-radio-button label="energy">能源</el-radio-button>
                    <el-radio-button label="agriculture">农产品</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
            </template>
            <div class="market-grid">
              <div class="market-item" v-for="item in filteredMarketData" :key="item.code">
                <div class="market-name">{{ item.name }}</div>
                <div class="market-price" :class="item.changeType">{{ item.price }}</div>
                <div class="market-change" :class="item.changeType">
                  <el-icon><component :is="item.changeType === 'up' ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                  {{ item.change }} ({{ item.changePercent }}%)
                </div>
                <div class="market-volume">成交量: {{ item.volume }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import ChangjiangFuturesSide from '~/components/layouts/ChangjiangFuturesSide.vue'

// 响应式数据
const currentTime = ref('')
const searchKeyword = ref('')
const trendPeriod = ref('7d')
const marketCategory = ref('all')
const hasRiskAlert = ref(true)
const riskAlertMessage = ref('检测到3个高风险持仓，请及时关注并采取相应措施')

// 核心指标数据
const coreMetrics = ref([
  {
    label: '总资产',
    value: '5.24亿',
    change: '+5.2%',
    changeType: 'positive',
    percentage: 85,
    progressColor: '#67c23a',
    icon: 'Wallet',
    status: 'normal'
  },
  {
    label: '净资产',
    value: '3.87亿',
    change: '-2.1%',
    changeType: 'negative',
    percentage: 73,
    progressColor: '#e6a23c',
    icon: 'Money',
    status: 'warning'
  },
  {
    label: '风险度',
    value: '15.6%',
    change: '+0.8%',
    changeType: 'positive',
    percentage: 16,
    progressColor: '#f56c6c',
    icon: 'Shield',
    status: 'danger'
  },
  {
    label: '保证金',
    value: '1.23亿',
    change: '+3.4%',
    changeType: 'positive',
    percentage: 62,
    progressColor: '#409eff',
    icon: 'TrendCharts',
    status: 'normal'
  },
  {
    label: 'VaR值',
    value: '856万',
    change: '-1.2%',
    changeType: 'negative',
    percentage: 45,
    progressColor: '#909399',
    icon: 'Warning',
    status: 'normal'
  },
  {
    label: '压力测试',
    value: '通过',
    change: '0%',
    changeType: 'positive',
    percentage: 100,
    progressColor: '#67c23a',
    icon: 'Shield',
    status: 'normal'
  }
])

// 风险等级分布数据
const riskLevelData = ref([
  { level: '高风险', count: 3, percentage: 15, color: '#f56c6c', class: 'high' },
  { level: '中风险', count: 8, percentage: 40, color: '#e6a23c', class: 'medium' },
  { level: '低风险', count: 9, percentage: 45, color: '#67c23a', class: 'low' }
])

// 资金使用情况
const fundUsagePercentage = ref(65)
const fundUsageColor = computed(() => {
  if (fundUsagePercentage.value > 80) return '#f56c6c'
  if (fundUsagePercentage.value > 60) return '#e6a23c'
  return '#67c23a'
})

// 风险趋势数据
const trendData = ref([
  { date: '12-10', value: 12.3, status: 'normal' },
  { date: '12-11', value: 13.8, status: 'normal' },
  { date: '12-12', value: 15.2, status: 'warning' },
  { date: '12-13', value: 14.6, status: 'normal' },
  { date: '12-14', value: 16.1, status: 'warning' },
  { date: '12-15', value: 15.6, status: 'warning' },
  { date: '今日', value: 15.6, status: 'warning' }
])

// 持仓风险数据
const riskData = ref([
  {
    product: '螺纹钢',
    contract: 'RB2405',
    position: 1000,
    marketValue: 4050,
    riskLevel: '中',
    riskValue: '125万',
    margin: 405,
    profitLoss: 50.5,
    status: '正常'
  },
  {
    product: '铜',
    contract: 'CU2405',
    position: 50,
    marketValue: 3475,
    riskLevel: '高',
    riskValue: '286万',
    margin: 347,
    profitLoss: -25.8,
    status: '预警'
  },
  {
    product: '原油',
    contract: 'SC2405',
    position: 20,
    marketValue: 1180,
    riskLevel: '高',
    riskValue: '198万',
    margin: 118,
    profitLoss: -15.2,
    status: '预警'
  },
  {
    product: '铁矿石',
    contract: 'I2405',
    position: 80,
    marketValue: 6560,
    riskLevel: '低',
    riskValue: '89万',
    margin: 656,
    profitLoss: 160.3,
    status: '正常'
  },
  {
    product: '焦炭',
    contract: 'J2405',
    position: 60,
    marketValue: 1500,
    riskLevel: '中',
    riskValue: '156万',
    margin: 150,
    profitLoss: 30.8,
    status: '正常'
  }
])

// 风险预警数据
const riskAlerts = ref([
  {
    id: 1,
    level: 'high',
    title: '铜期货持仓风险过高',
    description: '当前持仓风险值已超过预警线',
    time: '2分钟前'
  },
  {
    id: 2,
    level: 'medium',
    title: '原油价格波动加剧',
    description: '建议关注持仓风险变化',
    time: '5分钟前'
  },
  {
    id: 3,
    level: 'high',
    title: '保证金使用率偏高',
    description: '当前使用率65%，接近预警线',
    time: '10分钟前'
  },
  {
    id: 4,
    level: 'medium',
    title: '螺纹钢基差扩大',
    description: '现货与期货价差异常',
    time: '15分钟前'
  }
])

// 市场行情数据
const marketData = ref([
  {
    code: 'RB2405',
    name: '螺纹钢',
    price: '4050',
    change: '+25',
    changePercent: '+0.62',
    changeType: 'up',
    volume: '1.2万手',
    category: 'metal'
  },
  {
    code: 'CU2405',
    name: '沪铜',
    price: '69500',
    change: '-150',
    changePercent: '-0.22',
    changeType: 'down',
    volume: '8.5千手',
    category: 'metal'
  },
  {
    code: 'SC2405',
    name: '原油',
    price: '590',
    change: '-8',
    changePercent: '-1.34',
    changeType: 'down',
    volume: '3.2万手',
    category: 'energy'
  },
  {
    code: 'I2405',
    name: '铁矿石',
    price: '820',
    change: '+12',
    changePercent: '+1.48',
    changeType: 'up',
    volume: '2.8万手',
    category: 'metal'
  },
  {
    code: 'C2405',
    name: '玉米',
    price: '2680',
    change: '+15',
    changePercent: '+0.56',
    changeType: 'up',
    volume: '1.5万手',
    category: 'agriculture'
  },
  {
    code: 'A2405',
    name: '豆一',
    price: '4250',
    change: '-20',
    changePercent: '-0.47',
    changeType: 'down',
    volume: '9.8千手',
    category: 'agriculture'
  }
])

// 计算属性
const filteredRiskData = computed(() => {
  if (!searchKeyword.value) return riskData.value
  return riskData.value.filter(item =>
    item.product.includes(searchKeyword.value) ||
    item.contract.includes(searchKeyword.value)
  )
})

const filteredMarketData = computed(() => {
  if (marketCategory.value === 'all') return marketData.value
  return marketData.value.filter(item => item.category === marketCategory.value)
})

const alertCount = computed(() => riskAlerts.value.length)

// 方法
const updateCurrentTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const refreshData = () => {
  ElMessage.success('数据刷新成功')
  updateCurrentTime()
}

const getRiskLevelType = (level: string) => {
  const typeMap: Record<string, string> = {
    '高': 'danger',
    '中': 'warning',
    '低': 'success'
  }
  return typeMap[level] || 'info'
}

const viewDetail = (row: any) => {
  ElMessage.info(`查看 ${row.product} 详细信息`)
}

// 生命周期
onMounted(() => {
  updateCurrentTime()
  // 每30秒更新一次时间
  setInterval(updateCurrentTime, 30000)
})
</script>

<style scoped>
.risk-dashboard-container {
  display: flex;
  height: 100vh;
  background-color: #f0f2f5;
}

.content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.update-time {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  opacity: 0.9;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 风险预警 */
.risk-alert {
  margin-bottom: 20px;
}

/* 核心指标 */
.metrics-section {
  margin-bottom: 20px;
}

.metric-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.metric-card.normal {
  border-left: 4px solid #67c23a;
}

.metric-card.warning {
  border-left: 4px solid #e6a23c;
}

.metric-card.danger {
  border-left: 4px solid #f56c6c;
}

.metric-icon {
  margin-right: 16px;
  color: #409eff;
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
}

.metric-change.positive {
  color: #67c23a;
}

.metric-change.negative {
  color: #f56c6c;
}

.metric-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

/* 监控面板 */
.monitoring-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 320px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

/* 风险等级分布 */
.risk-level-chart {
  padding: 20px 0;
}

.risk-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 12px;
}

.risk-level {
  width: 60px;
  font-size: 12px;
  font-weight: 600;
}

.risk-level.high {
  color: #f56c6c;
}

.risk-level.medium {
  color: #e6a23c;
}

.risk-level.low {
  color: #67c23a;
}

.risk-count {
  width: 40px;
  font-size: 14px;
  font-weight: 600;
}

.risk-bar {
  flex: 1;
  height: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  overflow: hidden;
}

.risk-bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.risk-percentage {
  width: 40px;
  text-align: right;
  font-size: 12px;
  color: #909399;
}

/* 资金使用情况 */
.fund-usage {
  display: flex;
  align-items: center;
  padding: 20px;
  gap: 30px;
}

.fund-circle {
  flex-shrink: 0;
}

.percentage-value {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #303133;
}

.percentage-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.fund-details {
  flex: 1;
}

.fund-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.fund-label {
  color: #909399;
  font-size: 14px;
}

.fund-value {
  font-weight: 600;
  font-size: 14px;
}

.fund-value.used {
  color: #e6a23c;
}

.fund-value.available {
  color: #67c23a;
}

/* 风险趋势 */
.trend-chart {
  padding: 20px;
}

.trend-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.trend-line:last-child {
  border-bottom: none;
}

.trend-date {
  font-size: 12px;
  color: #909399;
  width: 60px;
}

.trend-value {
  font-weight: 600;
  font-size: 14px;
}

.trend-value.normal {
  color: #67c23a;
}

.trend-value.warning {
  color: #e6a23c;
}

.trend-value.danger {
  color: #f56c6c;
}

/* 持仓监控 */
.position-section {
  margin-bottom: 20px;
}

.table-card {
  height: auto;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.profit {
  color: #67c23a;
  font-weight: 600;
}

.loss {
  color: #f56c6c;
  font-weight: 600;
}

/* 风险预警列表 */
.alert-card {
  height: 400px;
}

.alert-list {
  max-height: 320px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.alert-item:hover {
  background-color: #f8f9fa;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.alert-icon.high {
  color: #f56c6c;
}

.alert-icon.medium {
  color: #e6a23c;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.alert-desc {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.alert-time {
  font-size: 11px;
  color: #909399;
}

.alert-action {
  margin-left: 8px;
}

.alert-badge {
  margin-left: 8px;
}

/* 市场行情 */
.market-section {
  margin-bottom: 20px;
}

.market-card {
  height: auto;
}

.market-tabs {
  display: flex;
  align-items: center;
}

.market-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  padding: 20px;
}

.market-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
  transition: all 0.2s;
}

.market-item:hover {
  background: #ecf5ff;
  transform: translateY(-1px);
}

.market-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.market-price {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 4px;
}

.market-price.up {
  color: #f56c6c;
}

.market-price.down {
  color: #67c23a;
}

.market-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
}

.market-change.up {
  color: #f56c6c;
}

.market-change.down {
  color: #67c23a;
}

.market-volume {
  font-size: 11px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .metric-card {
    padding: 16px;
  }

  .metric-value {
    font-size: 24px;
  }

  .market-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-right {
    justify-content: center;
  }

  .fund-usage {
    flex-direction: column;
    text-align: center;
  }

  .market-grid {
    grid-template-columns: 1fr;
  }
}
</style>
