<template>
  <div class="risk-dashboard-container">
    <!-- 左侧菜单 -->
    <ChangjiangFuturesSide />

    <!-- 主体内容区域 -->
    <div class="content">
      <el-card class="dashboard-card">
        <template #header>
          <div class="card-header">
            <span>风险管理驾驶舱</span>
            <el-button type="primary" size="small">刷新数据</el-button>
          </div>
        </template>
        
        <!-- 关键指标卡片 -->
        <el-row :gutter="20" class="metrics-row">
          <el-col :span="6">
            <el-card shadow="hover" class="metric-card">
              <div class="metric-content">
                <div class="metric-value">¥ 1,234,567</div>
                <div class="metric-label">总资产</div>
                <div class="metric-change positive">+5.2%</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="metric-card">
              <div class="metric-content">
                <div class="metric-value">¥ 987,654</div>
                <div class="metric-label">净资产</div>
                <div class="metric-change negative">-2.1%</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="metric-card">
              <div class="metric-content">
                <div class="metric-value">15.6%</div>
                <div class="metric-label">风险度</div>
                <div class="metric-change positive">+0.8%</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="metric-card">
              <div class="metric-content">
                <div class="metric-value">¥ 456,789</div>
                <div class="metric-label">保证金</div>
                <div class="metric-change positive">+3.4%</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 图表区域 -->
        <el-row :gutter="20" class="charts-row">
          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <span>风险趋势图</span>
              </template>
              <div class="chart-placeholder">
                <div class="chart-content">风险趋势图表区域</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <span>资产分布</span>
              </template>
              <div class="chart-placeholder">
                <div class="chart-content">资产分布图表区域</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 数据表格 -->
        <el-card shadow="hover" class="table-card">
          <template #header>
            <span>风险监控明细</span>
          </template>
          <el-table :data="riskData" style="width: 100%">
            <el-table-column prop="product" label="产品名称" width="180" />
            <el-table-column prop="riskLevel" label="风险等级" width="120" />
            <el-table-column prop="exposure" label="风险敞口" width="150" />
            <el-table-column prop="margin" label="保证金" width="150" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === '正常' ? 'success' : 'warning'">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="updateTime" label="更新时间" />
          </el-table>
        </el-card>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ChangjiangFuturesSide from '~/components/layouts/ChangjiangFuturesSide.vue'

const riskData = ref([
  {
    product: '螺纹钢2405',
    riskLevel: '中',
    exposure: '¥ 1,234,567',
    margin: '¥ 123,456',
    status: '正常',
    updateTime: '2024-01-15 14:30:00'
  },
  {
    product: '铜2405',
    riskLevel: '高',
    exposure: '¥ 2,345,678',
    margin: '¥ 234,567',
    status: '预警',
    updateTime: '2024-01-15 14:25:00'
  },
  {
    product: '原油2405',
    riskLevel: '低',
    exposure: '¥ 987,654',
    margin: '¥ 98,765',
    status: '正常',
    updateTime: '2024-01-15 14:20:00'
  }
])
</script>

<style scoped>
.risk-dashboard-container {
  display: flex;
  height: 100vh;
}

.content {
  flex: 1;
  padding: 20px;
  background-color: #f5f5f5;
}

.dashboard-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.metrics-row {
  margin-bottom: 20px;
}

.metric-card {
  text-align: center;
}

.metric-content {
  padding: 10px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.metric-change {
  font-size: 12px;
  font-weight: bold;
}

.metric-change.positive {
  color: #67c23a;
}

.metric-change.negative {
  color: #f56c6c;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 2px dashed #ddd;
}

.chart-content {
  color: #999;
  font-size: 16px;
}

.table-card {
  margin-top: 20px;
}
</style>
