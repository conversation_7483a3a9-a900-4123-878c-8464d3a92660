<template>
  <SubHeader/>
  <div class="report-table">
    <el-row :gutter="20">
      <!-- 左侧菜单 -->
      <el-col :span="2">
         <el-menu
          :default-active="activeMenu"
          @select="handleMenuSelect"
          class="menu-style"
          unique-opened
        >
          <!-- 遍历一级菜单 -->
          <template v-for="item in menuItems" :key="item.id">
            <!-- 如果没有子菜单，则直接显示一级菜单项 -->
            <el-menu-item v-if="!item.children" :index="item.id">
              <span>{{ item.label }}</span>
            </el-menu-item>

            <!-- 如果有子菜单，则显示为 el-sub-menu -->
            <el-sub-menu v-else :index="item.id">
              <template #title>
                <span>{{ item.label }}</span>
              </template>

              <!-- 遍历二级菜单 -->
              <el-menu-item
                v-for="child in item.children"
                :key="child.id"
                :index="child.id"
              >
                <span style="margin-left: 10px;">{{ child.label }}</span>
              </el-menu-item>
            </el-sub-menu>
          </template>
        </el-menu>
      </el-col>

      <!-- 右侧内容 -->
      <el-col :span="20">
        <el-card class="box-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>逻辑推演</span>
            </div>
          </template>
          <div class="image-wrapper">
            <img src="/images/logical-deduction.png" alt="逻辑推演图示" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import SubHeader from '~/components/layouts/SubHeader.vue'
import { ref, onMounted } from 'vue'
import { Search, Postcard, DocumentAdd } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()

// 定义菜单项类型
interface MenuItem {
  id: string
  label: string
  children?: MenuItem[] // 可选属性
}

// 菜单相关
const activeMenu = ref('0')

const menuItems = ref<MenuItem[]>([
  {
    id: '0',
    label: '逻辑推演'
  },
  {
    id: '1',
    label: '研报列表',
    children: [
      { id: '2-1', label: '周报' },
      { id: '2-2', label: '月报' },
      { id: '2-3', label: '年报' },
      { id: '2-4', label: '专题' },
      { id: '2-5', label: '调研' },
      { id: '2-6', label: '策略' }
    ]
  },
  {
    id: '2',
    label: '数据图表',
  }
])

const handleMenuSelect = (index: string) => {
  console.log('选中的菜单项:', index)

  if (index === '0') {
    router.push('/logical-deduction')
  } else if (index === '1') {
    router.push('/report')
  } else if (index.startsWith('2-')) {
    router.push('/report')
  } else if (index === '2') {
    router.push('/data-chart')
  }
}


</script>

<style scoped>
.logical-deduction-container {
  padding: 20px;
}

.image-wrapper {
  display: flex;
  justify-content: center;
}

.image-wrapper img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}

:deep(.box-card) {
  border: none;
  box-shadow: none;
}
</style>