<template>
  <div class="industry-dashboard-container">
    <!-- 左侧菜单 -->
    <ChangjiangIndustrySide />

    <!-- 主体内容区域 -->
    <div class="content">
      <el-card class="dashboard-card">
        <template #header>
          <div class="card-header">
            <span>长江产业驾驶舱</span>
            <div class="header-actions">
              <el-button type="primary" size="small">数据刷新</el-button>
              <el-button type="success" size="small">导出报告</el-button>
            </div>
          </div>
        </template>
        
        <!-- 关键指标卡片 -->
        <el-row :gutter="20" class="metrics-row">
          <el-col :span="6">
            <el-card shadow="hover" class="metric-card">
              <div class="metric-content">
                <div class="metric-value">¥ 8,765,432</div>
                <div class="metric-label">产业总资产</div>
                <div class="metric-change positive">+8.5%</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="metric-card">
              <div class="metric-content">
                <div class="metric-value">156</div>
                <div class="metric-label">合作企业数</div>
                <div class="metric-change positive">+12</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="metric-card">
              <div class="metric-content">
                <div class="metric-value">¥ 2,345,678</div>
                <div class="metric-label">月度交易额</div>
                <div class="metric-change positive">+15.3%</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="metric-card">
              <div class="metric-content">
                <div class="metric-value">98.5%</div>
                <div class="metric-label">服务满意度</div>
                <div class="metric-change positive">+0.8%</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 图表区域 -->
        <el-row :gutter="20" class="charts-row">
          <el-col :span="8">
            <el-card shadow="hover">
              <template #header>
                <span>产业链分布</span>
              </template>
              <div class="chart-placeholder">
                <div class="chart-content">产业链分布饼图</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover">
              <template #header>
                <span>月度业务趋势</span>
              </template>
              <div class="chart-placeholder">
                <div class="chart-content">业务趋势折线图</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover">
              <template #header>
                <span>风险监控</span>
              </template>
              <div class="chart-placeholder">
                <div class="chart-content">风险监控仪表盘</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 业务概览 -->
        <el-row :gutter="20" class="business-overview">
          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <span>重点合作企业</span>
              </template>
              <el-table :data="keyPartners" style="width: 100%">
                <el-table-column prop="companyName" label="企业名称" width="150" />
                <el-table-column prop="industry" label="行业" width="100" />
                <el-table-column prop="cooperationLevel" label="合作等级" width="100">
                  <template #default="scope">
                    <el-tag :type="getCooperationLevelTag(scope.row.cooperationLevel)">
                      {{ scope.row.cooperationLevel }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="monthlyVolume" label="月度交易量" />
              </el-table>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <span>业务分类统计</span>
              </template>
              <el-table :data="businessStats" style="width: 100%">
                <el-table-column prop="businessType" label="业务类型" width="120" />
                <el-table-column prop="count" label="数量" width="80" />
                <el-table-column prop="volume" label="交易量(万元)" width="120" />
                <el-table-column prop="growth" label="增长率" width="100">
                  <template #default="scope">
                    <span :class="scope.row.growth >= 0 ? 'positive-growth' : 'negative-growth'">
                      {{ scope.row.growth }}%
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
        </el-row>

        <!-- 实时动态 -->
        <el-card shadow="hover" class="news-card">
          <template #header>
            <span>实时动态</span>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in newsData"
              :key="index"
              :timestamp="item.time"
              :type="item.type"
            >
              <h4>{{ item.title }}</h4>
              <p>{{ item.content }}</p>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ChangjiangIndustrySide from '~/components/layouts/ChangjiangIndustrySide.vue'

const keyPartners = ref([
  {
    companyName: '钢铁集团A',
    industry: '钢铁',
    cooperationLevel: '战略',
    monthlyVolume: '¥ 5,000万'
  },
  {
    companyName: '有色金属B',
    industry: '有色',
    cooperationLevel: '核心',
    monthlyVolume: '¥ 3,200万'
  },
  {
    companyName: '能源公司C',
    industry: '能源',
    cooperationLevel: '重要',
    monthlyVolume: '¥ 2,800万'
  },
  {
    companyName: '化工企业D',
    industry: '化工',
    cooperationLevel: '一般',
    monthlyVolume: '¥ 1,500万'
  }
])

const businessStats = ref([
  {
    businessType: '供应链金融',
    count: 45,
    volume: 15000,
    growth: 12.5
  },
  {
    businessType: '贸易融资',
    count: 32,
    volume: 8500,
    growth: 8.3
  },
  {
    businessType: '风险管理',
    count: 28,
    volume: 6200,
    growth: -2.1
  },
  {
    businessType: '仓储物流',
    count: 18,
    volume: 3800,
    growth: 15.7
  }
])

const newsData = ref([
  {
    time: '2024-01-15 14:30',
    type: 'primary',
    title: '新增战略合作伙伴',
    content: '与某大型钢铁集团签署战略合作协议，预计年交易额达10亿元'
  },
  {
    time: '2024-01-15 13:45',
    type: 'success',
    title: '风险管控系统升级',
    content: '产业风险管控系统完成升级，新增智能预警功能'
  },
  {
    time: '2024-01-15 12:20',
    type: 'warning',
    title: '市场价格波动提醒',
    content: '螺纹钢期货价格出现较大波动，建议关注相关风险敞口'
  },
  {
    time: '2024-01-15 11:15',
    type: 'info',
    title: '月度业务报告发布',
    content: '12月份产业金融业务报告已发布，整体业绩超预期'
  }
])

const getCooperationLevelTag = (level: string) => {
  const tagMap: Record<string, string> = {
    '战略': 'danger',
    '核心': 'warning',
    '重要': 'success',
    '一般': 'info'
  }
  return tagMap[level] || ''
}
</script>

<style scoped>
.industry-dashboard-container {
  display: flex;
  height: 100vh;
}

.content {
  flex: 1;
  padding: 20px;
  background-color: #f5f5f5;
}

.dashboard-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.metrics-row {
  margin-bottom: 20px;
}

.metric-card {
  text-align: center;
}

.metric-content {
  padding: 10px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.metric-change {
  font-size: 12px;
  font-weight: bold;
}

.metric-change.positive {
  color: #67c23a;
}

.metric-change.negative {
  color: #f56c6c;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-placeholder {
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 2px dashed #ddd;
}

.chart-content {
  color: #999;
  font-size: 16px;
}

.business-overview {
  margin-bottom: 20px;
}

.news-card {
  margin-top: 20px;
}

.positive-growth {
  color: #67c23a;
  font-weight: bold;
}

.negative-growth {
  color: #f56c6c;
  font-weight: bold;
}
</style>
