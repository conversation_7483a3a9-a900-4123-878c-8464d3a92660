<template>
  <div class="asset-penetration-container">
      <el-card class="main-card">
        <template #header>
          <div class="card-header">
            <span>资管产品穿透持仓表</span>
            <div class="header-actions">
              <el-button type="primary" size="small">导出Excel</el-button>
              <el-button type="success" size="small">刷新数据</el-button>
            </div>
          </div>
        </template>

        <!-- 筛选条件 -->
        <div class="filter-section">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input v-model="filterForm.productCode" placeholder="产品代码" clearable />
            </el-col>
            <el-col :span="6">
              <el-input v-model="filterForm.productName" placeholder="产品名称" clearable />
            </el-col>
            <el-col :span="6">
              <el-select v-model="filterForm.assetType" placeholder="资产类型" clearable>
                <el-option label="股票" value="stock" />
                <el-option label="债券" value="bond" />
                <el-option label="基金" value="fund" />
                <el-option label="期货" value="futures" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 数据表格 -->
        <el-table
          :data="penetrationData"
          style="width: 100%"
          v-loading="loading"
          row-key="id"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        >
          <el-table-column prop="productCode" label="产品代码" width="120" />
          <el-table-column prop="productName" label="产品名称" width="200" />
          <el-table-column prop="assetCode" label="资产代码" width="120" />
          <el-table-column prop="assetName" label="资产名称" width="180" />
          <el-table-column prop="assetType" label="资产类型" width="100">
            <template #default="scope">
              <el-tag :type="getAssetTypeTag(scope.row.assetType)">
                {{ getAssetTypeName(scope.row.assetType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="holdingAmount" label="持仓数量" width="120" />
          <el-table-column prop="marketValue" label="市值(万元)" width="120" />
          <el-table-column prop="proportion" label="占比(%)" width="100" />
          <el-table-column prop="costPrice" label="成本价" width="100" />
          <el-table-column prop="currentPrice" label="现价" width="100" />
          <el-table-column prop="profitLoss" label="盈亏(万元)" width="120">
            <template #default="scope">
              <span :class="scope.row.profitLoss >= 0 ? 'profit' : 'loss'">
                {{ scope.row.profitLoss }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="150" />
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const loading = ref(false)

const filterForm = reactive({
  productCode: '',
  productName: '',
  assetType: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 50
})

const penetrationData = ref([
  {
    id: 1,
    productCode: 'CJ001',
    productName: '长江稳健增长1号',
    assetCode: '000001',
    assetName: '平安银行',
    assetType: 'stock',
    holdingAmount: '100000',
    marketValue: '1234.56',
    proportion: '15.6',
    costPrice: '12.34',
    currentPrice: '12.56',
    profitLoss: '22.00',
    updateTime: '2024-01-15 14:30:00',
    children: [
      {
        id: 11,
        productCode: 'CJ001',
        productName: '长江稳健增长1号',
        assetCode: '000002',
        assetName: '万科A',
        assetType: 'stock',
        holdingAmount: '50000',
        marketValue: '567.89',
        proportion: '7.2',
        costPrice: '11.36',
        currentPrice: '11.58',
        profitLoss: '11.00',
        updateTime: '2024-01-15 14:30:00'
      }
    ]
  },
  {
    id: 2,
    productCode: 'CJ002',
    productName: '长江价值精选2号',
    assetCode: '110001',
    assetName: '国债2024',
    assetType: 'bond',
    holdingAmount: '200000',
    marketValue: '2000.00',
    proportion: '25.0',
    costPrice: '100.00',
    currentPrice: '100.50',
    profitLoss: '100.00',
    updateTime: '2024-01-15 14:25:00'
  },
  {
    id: 3,
    productCode: 'CJ003',
    productName: '长江债券优选3号',
    assetCode: 'RB2405',
    assetName: '螺纹钢2405',
    assetType: 'futures',
    holdingAmount: '10',
    marketValue: '400.00',
    proportion: '5.3',
    costPrice: '4000.00',
    currentPrice: '3950.00',
    profitLoss: '-5.00',
    updateTime: '2024-01-15 14:20:00'
  }
])

const getAssetTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    stock: 'danger',
    bond: 'success',
    fund: 'warning',
    futures: 'info'
  }
  return tagMap[type] || ''
}

const getAssetTypeName = (type: string) => {
  const nameMap: Record<string, string> = {
    stock: '股票',
    bond: '债券',
    fund: '基金',
    futures: '期货'
  }
  return nameMap[type] || type
}

const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询完成')
  }, 1000)
}

const handleReset = () => {
  Object.assign(filterForm, {
    productCode: '',
    productName: '',
    assetType: ''
  })
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  handleSearch()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  handleSearch()
}
</script>

<style scoped>
.asset-penetration-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.main-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.profit {
  color: #67c23a;
  font-weight: bold;
}

.loss {
  color: #f56c6c;
  font-weight: bold;
}
</style>
