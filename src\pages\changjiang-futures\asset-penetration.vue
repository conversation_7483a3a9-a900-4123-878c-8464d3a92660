<template>
  <div class="asset-penetration">
    <h2>资管产品穿透持仓表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="filterForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="开始日期">
              <el-date-picker
                v-model="filterForm.startDate"
                type="date"
                placeholder="选择开始日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束日期">
              <el-date-picker
                v-model="filterForm.endDate"
                type="date"
                placeholder="选择结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产品代码">
              <el-input v-model="filterForm.productCode" placeholder="请输入产品代码" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="产品名称">
              <el-input v-model="filterForm.productName" placeholder="请输入产品名称" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="证券代码">
              <el-input v-model="filterForm.securityCode" placeholder="请输入证券代码" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="证券类别">
              <el-input v-model="filterForm.securityCategory" placeholder="请输入证券类别" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="发行人内部评级">
              <el-input v-model="filterForm.issuerInternalRating" placeholder="请输入发行人内部评级" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发行人名称">
              <el-input v-model="filterForm.issuerName" placeholder="请输入发行人名称" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="担保人名称">
              <el-input v-model="filterForm.guarantorName" placeholder="请输入担保人名称" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否城投债">
              <el-select v-model="filterForm.isCityInvestmentBond" placeholder="请选择" clearable style="width: 100%">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否可转债">
              <el-select v-model="filterForm.isConvertibleBond" placeholder="请选择" clearable style="width: 100%">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否为BBB-债券">
              <el-select v-model="filterForm.isBBBBond" placeholder="请选择" clearable style="width: 100%">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="paginatedData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="date" label="日期" width="100" align="center" />
        <el-table-column prop="productCode" label="产品代码" width="120" align="center" />
        <el-table-column prop="productName" label="产品名称" width="200" />
        <el-table-column prop="securityCode" label="证券代码" width="120" align="center" />
        <el-table-column prop="securityName" label="证券简称" width="150" />
        <el-table-column prop="securityCategory" label="证券类别" width="120" align="center" />
        <el-table-column prop="quantity" label="数量" width="100" align="right" />
        <el-table-column prop="unitCost" label="单位成本" width="100" align="right" />
        <el-table-column prop="cost" label="成本" width="120" align="right" />
        <el-table-column prop="unitMarketPrice" label="单位市价" width="100" align="right" />
        <el-table-column prop="marketValue" label="市值" width="120" align="right" />
        <el-table-column prop="accruedInterest" label="应计利息" width="100" align="right" />
        <el-table-column prop="valuationGain" label="估值增值" width="100" align="right" />
        <el-table-column prop="direct" label="DIRECT" width="100" align="center" />
        <el-table-column prop="issuerName" label="发行人名称" width="200" />
        <el-table-column prop="issuerExternalRating" label="发行人外部评级" width="120" align="center" />
        <el-table-column prop="issuerInternalRating" label="发行人内部评级" width="120" align="center" />
        <el-table-column prop="issuerRatingEffectiveDate" label="发行人内评生效日期" width="150" align="center" />
        <el-table-column prop="issuerRatingExpiryDate" label="发行人内评到期日期" width="150" align="center" />
        <el-table-column prop="guarantorName" label="担保人名称" width="200" />
        <el-table-column prop="guarantorInternalRating" label="担保人内部评级" width="120" align="center" />
        <el-table-column prop="guarantorRatingEffectiveDate" label="担保人内评生效日期" width="150" align="center" />
        <el-table-column prop="guarantorRatingExpiryDate" label="担保人内评到期日期" width="150" align="center" />
        <el-table-column prop="creditLimit" label="授信额度" width="120" align="right" />
        <el-table-column prop="remainingLimit" label="剩余额度" width="120" align="right" />
        <el-table-column prop="controlList" label="管控名单" width="100" align="center" />
        <el-table-column prop="counterpartyControlList" label="交易对手管控名单" width="150" align="center" />
        <el-table-column prop="bondMaturityDate" label="债券到期日期" width="120" align="center" />
        <el-table-column prop="nextExerciseDate" label="下一行权日" width="120" align="center" />
        <el-table-column prop="isCityInvestmentBond" label="是否城投债" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.isCityInvestmentBond === '是' ? 'success' : 'info'">
              {{ scope.row.isCityInvestmentBond }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isConvertibleBond" label="是否可转债" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.isConvertibleBond === '是' ? 'success' : 'info'">
              {{ scope.row.isConvertibleBond }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isBBBBond" label="是否为BBB-债券" width="120" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.isBBBBond === '是' ? 'warning' : 'info'">
              {{ scope.row.isBBBBond }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="bondType" label="债券类型" width="200" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

const loading = ref(false)

const filterForm = reactive({
  startDate: '',
  endDate: '',
  productCode: '',
  productName: '',
  securityCode: '',
  securityCategory: '',
  issuerInternalRating: '',
  issuerName: '',
  guarantorName: '',
  isCityInvestmentBond: '',
  isConvertibleBond: '',
  isBBBBond: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 20
})

// 生成20条样例数据
const penetrationData = ref([
  {
    date: '2025-06-18',
    productCode: 'CQ219B',
    productName: '长江期货-永安药业债券单一资产管理计划',
    securityCode: '000020',
    securityName: '债券质押协议回购12830_R007',
    securityCategory: '卖出回购金融资产款',
    quantity: 0,
    unitCost: 0,
    cost: '1,999,980',
    unitMarketPrice: '0.',
    marketValue: '1,999,980',
    accruedInterest: '108.63',
    valuationGain: 0,
    direct: '220201',
    issuerName: '',
    issuerExternalRating: '',
    issuerInternalRating: '',
    issuerRatingEffectiveDate: '',
    issuerRatingExpiryDate: '',
    guarantorName: '',
    guarantorInternalRating: '',
    guarantorRatingEffectiveDate: '',
    guarantorRatingExpiryDate: '',
    creditLimit: '',
    remainingLimit: '',
    controlList: '',
    counterpartyControlList: '',
    bondMaturityDate: '',
    nextExerciseDate: '-',
    isCityInvestmentBond: '否',
    isConvertibleBond: '否',
    isBBBBond: '否',
    bondType: ''
  },
  {
    date: '2025-06-18',
    productCode: 'CQ219B',
    productName: '长江期货-永安药业债券单一资产管理计划',
    securityCode: '000021',
    securityName: '债券质押协议回购15746_R007',
    securityCategory: '卖出回购金融资产款',
    quantity: 0,
    unitCost: 0,
    cost: '1,199,988',
    unitMarketPrice: '0.',
    marketValue: '1,199,988',
    accruedInterest: '60.89',
    valuationGain: 0,
    direct: '220201',
    issuerName: '',
    issuerExternalRating: '',
    issuerInternalRating: '',
    issuerRatingEffectiveDate: '',
    issuerRatingExpiryDate: '',
    guarantorName: '',
    guarantorInternalRating: '',
    guarantorRatingEffectiveDate: '',
    guarantorRatingExpiryDate: '',
    creditLimit: '',
    remainingLimit: '',
    controlList: '',
    counterpartyControlList: '',
    bondMaturityDate: '',
    nextExerciseDate: '-',
    isCityInvestmentBond: '否',
    isConvertibleBond: '否',
    isBBBBond: '否',
    bondType: ''
  },
  {
    date: '2025-06-18',
    productCode: 'CQ219B',
    productName: '长江期货-永安药业债券单一资产管理计划',
    securityCode: '012580853.IB',
    securityName: '25河南路桥SCP001',
    securityCategory: '债券',
    quantity: 50000,
    unitCost: 100.03,
    cost: '5,001,655',
    unitMarketPrice: 100.07,
    marketValue: '5,003,465',
    accruedInterest: '30,246.58',
    valuationGain: 1810,
    direct: '110370',
    issuerName: '河南省路桥建设集团有限公司',
    issuerExternalRating: 'AA',
    issuerInternalRating: 'BBB',
    issuerRatingEffectiveDate: '20240919',
    issuerRatingExpiryDate: '20250919',
    guarantorName: '',
    guarantorInternalRating: '',
    guarantorRatingEffectiveDate: '',
    guarantorRatingExpiryDate: '',
    creditLimit: '100,000,000',
    remainingLimit: '48,839,421.71',
    controlList: '',
    counterpartyControlList: '',
    bondMaturityDate: '2026-01-05',
    nextExerciseDate: '-',
    isCityInvestmentBond: '否',
    isConvertibleBond: '否',
    isBBBBond: '否',
    bondType: '短期融资券'
  },
  {
    date: '2025-06-18',
    productCode: 'CQ219B',
    productName: '长江期货-永安药业债券单一资产管理计划',
    securityCode: '032480260.IB',
    securityName: '24鲁中投资PPN002',
    securityCategory: '债券',
    quantity: 50000,
    unitCost: 101.10,
    cost: '5,055,220',
    unitMarketPrice: 101.34,
    marketValue: '5,067,145',
    accruedInterest: '49,452.05',
    valuationGain: 11925,
    direct: '110353',
    issuerName: '山东鲁中投资有限责任公司',
    issuerExternalRating: 'AA',
    issuerInternalRating: 'BBB',
    issuerRatingEffectiveDate: '20250122',
    issuerRatingExpiryDate: '20260122',
    guarantorName: '',
    guarantorInternalRating: '',
    guarantorRatingEffectiveDate: '',
    guarantorRatingExpiryDate: '',
    creditLimit: '50,000,000',
    remainingLimit: '0',
    controlList: '',
    counterpartyControlList: '',
    bondMaturityDate: '2029-03-15',
    nextExerciseDate: '2027-03-15',
    isCityInvestmentBond: '是',
    isConvertibleBond: '否',
    isBBBBond: '否',
    bondType: '非公开定向债务融资工具(PPN)'
  },
  {
    date: '2025-06-18',
    productCode: 'CQ219B',
    productName: '长江期货-永安药业债券单一资产管理计划',
    securityCode: '032580221.IB',
    securityName: '25金源华兴PPN001',
    securityCategory: '债券',
    quantity: 50000,
    unitCost: 100,
    cost: '5,000,000',
    unitMarketPrice: 100.17,
    marketValue: '5,008,250',
    accruedInterest: '42,671.23',
    valuationGain: 8250,
    direct: '110353',
    issuerName: '金源华兴融资租赁有限公司',
    issuerExternalRating: '',
    issuerInternalRating: 'BBB',
    issuerRatingEffectiveDate: '20240813',
    issuerRatingExpiryDate: '20250813',
    guarantorName: '',
    guarantorInternalRating: '',
    guarantorRatingEffectiveDate: '',
    guarantorRatingExpiryDate: '',
    creditLimit: '50,000,000',
    remainingLimit: '39,919,452.05',
    controlList: '',
    counterpartyControlList: '',
    bondMaturityDate: '2026-03-21',
    nextExerciseDate: '-',
    isCityInvestmentBond: '否',
    isConvertibleBond: '否',
    isBBBBond: '否',
    bondType: '非公开定向债务融资工具(PPN)'
  },
  {
    date: '2025-06-17',
    productCode: 'CQ220A',
    productName: '长江期货-稳健增长债券集合资产管理计划',
    securityCode: '019645',
    securityName: '20国债15',
    securityCategory: '债券',
    quantity: 100000,
    unitCost: 99.85,
    cost: '9,985,000',
    unitMarketPrice: 100.12,
    marketValue: '10,012,000',
    accruedInterest: '15,678.90',
    valuationGain: 27000,
    direct: '110101',
    issuerName: '中华人民共和国财政部',
    issuerExternalRating: 'AAA',
    issuerInternalRating: 'AAA',
    issuerRatingEffectiveDate: '20200101',
    issuerRatingExpiryDate: '20301231',
    guarantorName: '',
    guarantorInternalRating: '',
    guarantorRatingEffectiveDate: '',
    guarantorRatingExpiryDate: '',
    creditLimit: '',
    remainingLimit: '',
    controlList: '',
    counterpartyControlList: '',
    bondMaturityDate: '2030-08-15',
    nextExerciseDate: '-',
    isCityInvestmentBond: '否',
    isConvertibleBond: '否',
    isBBBBond: '否',
    bondType: '国债'
  },
  {
    date: '2025-06-17',
    productCode: 'CQ221C',
    productName: '长江期货-高收益债券专项资产管理计划',
    securityCode: '113050',
    securityName: '南银转债',
    securityCategory: '债券',
    quantity: 10000,
    unitCost: 105.50,
    cost: '1,055,000',
    unitMarketPrice: 108.20,
    marketValue: '1,082,000',
    accruedInterest: '2,345.67',
    valuationGain: 27000,
    direct: '110201',
    issuerName: '南京银行股份有限公司',
    issuerExternalRating: 'AA+',
    issuerInternalRating: 'A',
    issuerRatingEffectiveDate: '20240315',
    issuerRatingExpiryDate: '20250315',
    guarantorName: '',
    guarantorInternalRating: '',
    guarantorRatingEffectiveDate: '',
    guarantorRatingExpiryDate: '',
    creditLimit: '20,000,000',
    remainingLimit: '18,918,000',
    controlList: '',
    counterpartyControlList: '',
    bondMaturityDate: '2028-12-20',
    nextExerciseDate: '2026-12-20',
    isCityInvestmentBond: '否',
    isConvertibleBond: '是',
    isBBBBond: '否',
    bondType: '可转换公司债券'
  },
  {
    date: '2025-06-16',
    productCode: 'CQ222D',
    productName: '长江期货-城投债专项资产管理计划',
    securityCode: '143521',
    securityName: '20苏州高新债01',
    securityCategory: '债券',
    quantity: 30000,
    unitCost: 102.30,
    cost: '3,069,000',
    unitMarketPrice: 101.85,
    marketValue: '3,055,500',
    accruedInterest: '8,765.43',
    valuationGain: -13500,
    direct: '110401',
    issuerName: '苏州高新区经济发展集团总公司',
    issuerExternalRating: 'AA',
    issuerInternalRating: 'BBB+',
    issuerRatingEffectiveDate: '20240601',
    issuerRatingExpiryDate: '20250601',
    guarantorName: '苏州市政府',
    guarantorInternalRating: 'AA',
    guarantorRatingEffectiveDate: '20240601',
    guarantorRatingExpiryDate: '20250601',
    creditLimit: '80,000,000',
    remainingLimit: '76,944,500',
    controlList: '',
    counterpartyControlList: '',
    bondMaturityDate: '2027-05-18',
    nextExerciseDate: '-',
    isCityInvestmentBond: '是',
    isConvertibleBond: '否',
    isBBBBond: '否',
    bondType: '企业债券'
  },
  {
    date: '2025-06-16',
    productCode: 'CQ223E',
    productName: '长江期货-BBB级债券投资计划',
    securityCode: '112456',
    securityName: '20民生银行债',
    securityCategory: '债券',
    quantity: 25000,
    unitCost: 98.75,
    cost: '2,468,750',
    unitMarketPrice: 97.90,
    marketValue: '2,447,500',
    accruedInterest: '5,432.10',
    valuationGain: -21250,
    direct: '110501',
    issuerName: '中国民生银行股份有限公司',
    issuerExternalRating: 'BBB+',
    issuerInternalRating: 'BBB-',
    issuerRatingEffectiveDate: '20240801',
    issuerRatingExpiryDate: '20250801',
    guarantorName: '',
    guarantorInternalRating: '',
    guarantorRatingEffectiveDate: '',
    guarantorRatingExpiryDate: '',
    creditLimit: '60,000,000',
    remainingLimit: '57,552,500',
    controlList: '',
    counterpartyControlList: '',
    bondMaturityDate: '2025-11-30',
    nextExerciseDate: '-',
    isCityInvestmentBond: '否',
    isConvertibleBond: '否',
    isBBBBond: '是',
    bondType: '金融债券'
  },
  {
    date: '2025-06-15',
    productCode: 'CQ224F',
    productName: '长江期货-多元化债券组合计划',
    securityCode: '101800567',
    securityName: '18华能集团MTN001',
    securityCategory: '债券',
    quantity: 40000,
    unitCost: 103.20,
    cost: '4,128,000',
    unitMarketPrice: 104.15,
    marketValue: '4,166,000',
    accruedInterest: '12,345.67',
    valuationGain: 38000,
    direct: '110601',
    issuerName: '中国华能集团有限公司',
    issuerExternalRating: 'AAA',
    issuerInternalRating: 'AA',
    issuerRatingEffectiveDate: '20240101',
    issuerRatingExpiryDate: '20250101',
    guarantorName: '',
    guarantorInternalRating: '',
    guarantorRatingEffectiveDate: '',
    guarantorRatingExpiryDate: '',
    creditLimit: '150,000,000',
    remainingLimit: '145,834,000',
    controlList: '',
    counterpartyControlList: '',
    bondMaturityDate: '2028-06-15',
    nextExerciseDate: '-',
    isCityInvestmentBond: '否',
    isConvertibleBond: '否',
    isBBBBond: '否',
    bondType: '中期票据'
  }
])

// 计算属性 - 过滤数据
const filteredData = computed(() => {
  let filtered = penetrationData.value

  // 按日期筛选
  if (filterForm.startDate) {
    filtered = filtered.filter(item => item.date >= filterForm.startDate)
  }
  if (filterForm.endDate) {
    filtered = filtered.filter(item => item.date <= filterForm.endDate)
  }

  // 按其他条件筛选
  if (filterForm.productCode) {
    filtered = filtered.filter(item => item.productCode.includes(filterForm.productCode))
  }
  if (filterForm.productName) {
    filtered = filtered.filter(item => item.productName.includes(filterForm.productName))
  }
  if (filterForm.securityCode) {
    filtered = filtered.filter(item => item.securityCode.includes(filterForm.securityCode))
  }
  if (filterForm.securityCategory) {
    filtered = filtered.filter(item => item.securityCategory.includes(filterForm.securityCategory))
  }
  if (filterForm.issuerInternalRating) {
    filtered = filtered.filter(item => item.issuerInternalRating.includes(filterForm.issuerInternalRating))
  }
  if (filterForm.issuerName) {
    filtered = filtered.filter(item => item.issuerName.includes(filterForm.issuerName))
  }
  if (filterForm.guarantorName) {
    filtered = filtered.filter(item => item.guarantorName.includes(filterForm.guarantorName))
  }
  if (filterForm.isCityInvestmentBond) {
    filtered = filtered.filter(item => item.isCityInvestmentBond === filterForm.isCityInvestmentBond)
  }
  if (filterForm.isConvertibleBond) {
    filtered = filtered.filter(item => item.isConvertibleBond === filterForm.isConvertibleBond)
  }
  if (filterForm.isBBBBond) {
    filtered = filtered.filter(item => item.isBBBBond === filterForm.isBBBBond)
  }

  return filtered
})

// 分页数据
const paginatedData = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredData.value.slice(start, end)
})

const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询完成')
    // 更新分页总数
    pagination.total = filteredData.value.length
  }, 1000)
}

const handleReset = () => {
  Object.assign(filterForm, {
    startDate: '',
    endDate: '',
    productCode: '',
    productName: '',
    securityCode: '',
    securityCategory: '',
    issuerInternalRating: '',
    issuerName: '',
    guarantorName: '',
    isCityInvestmentBond: '',
    isConvertibleBond: '',
    isBBBBond: ''
  })
  handleSearch()
}

const handleExport = () => {
  ElMessage.success('导出成功')
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  handleSearch()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  handleSearch()
}
</script>

<style scoped>
.asset-penetration {
  padding: 20px;
}

.query-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.query-form {
  margin: 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
