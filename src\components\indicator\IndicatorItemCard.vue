<template>
  <el-card class="indicator-card" :body-style="{ padding: '0px' }" shadow="hover">
    <!-- 标题 -->
    <div class="title-wrapper">
      <h3 class="title">{{ indicator.name }}</h3>
      <el-icon size="20px" :style="{ color: indicator.isFavorite ? '#F56C6C' : '#999' }"  class="no-inherit"  style="margin-right: 15px;">
        <Star />
      </el-icon>
    </div>
    <div class="card-content" @click="$emit('click', indicator)">
      <!-- 封面图表 -->
      <div class="cover-chart" :ref="(el) => setChartRef(el, indicator.id)">
       <div :id="'chart-' + indicator.id" class="mini-echart"></div>
      </div>
      
     <!-- 报告内容 -->
    <div class="indicator-info">
       
        <!-- 元信息 -->
        <div class="meta-summary">
            <span class="board-name">{{ indicator.boardName }}</span>
            <span class="divider">｜</span>
            <span class="product-name">品种：{{ indicator.productName }}</span>
        </div>

        <!-- 摘要 -->
        <!-- <p class="summary">品种：{{ indicator.productName }}</p> -->
         <!-- 新增的数据显示 -->
        <div class="rate-info" :style="{ backgroundColor: getBackgroundColor(indicator.changeRate) }">
          最新值：<span style="margin-right: 10px;">{{ indicator.latestValue }}</span>
          变化率：<span >{{ indicator.changeRate }}%</span>
        </div>
        <!-- 操作按钮（保持不变） -->
        <div class="card-actions">            
            <!-- <el-button :icon="View" @click.stop="$emit('open-full-chart', indicator)" type="primary">
                查看
            </el-button> -->
            <el-button type="primary" plain @click.stop="$emit('open-full-chart', indicator)">查看</el-button>
        </div>
     </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { nextTick } from 'vue'
import { Star, View } from '@element-plus/icons-vue'
import defaultCover from '~/assets/images/default-cover.png'
import defaultAvatar from '~/assets/images/default-avatar.jpg'
// const defaultCover = new URL('/assets/images/default-cover.png', import.meta.url).href
// import { formatTime } from '@/utils/date'

import * as echarts from 'echarts'

const props = defineProps({
  indicator: {
    type: Object,
    required: true,
    default: () => ({
      id: 0,
      name: '',
      isFavorite: false,
      datasource: 'local',
      category: '月'
    })
  },
  index: {
    type: Number,
    default: 0
  }
})

const setChartRef = (el: HTMLElement, indicatorId: number | string) => {
  if (el) {
    const observer = new ResizeObserver(() => {
      observer.disconnect() // 只执行一次
      initChart(indicatorId)
    })

    observer.observe(el)
  }
}

const initChart = (indicatorId: number | string) => {
  const dom = document.getElementById(`chart-${indicatorId}`)
  if (!dom) return

  const chart = echarts.init(dom)

  // ✅ 改为 30 天数据
  const data = Array.from({ length: 30 }, () => Math.floor(Math.random() * 100))

  // ✅ X轴数据：['1日', '2日', ..., '30日']
  const xAxisData = Array.from({ length: 30 }, (_, i) => `${i + 1}日`)

  let option

  const isLineChart = Number(indicatorId) % 2 === 1

  if (isLineChart) {
    option = {
      tooltip: { show: false },
      xAxis: {
        type: 'category',
        data: xAxisData,
        show: false // 隐藏 x 轴
      },
      yAxis: {
        show: false // 隐藏 y 轴
      },
      grid: {
        top: 5,
        bottom: 5,
        left: 5,
        right: 5
      },
      series: [{
        type: 'line',
        smooth: true,
        data: data,
        lineStyle: { color: '#409EFF' },
        areaStyle: { color: 'rgba(64, 158, 255, 0.2)' },
        symbol: 'none'
      }]
    }
  } else {
    option = {
      tooltip: { show: false },
      xAxis: {
        type: 'category',
        data: xAxisData,
        show: false // 隐藏 x 轴
      },
      yAxis: {
        show: false // 隐藏 y 轴
      },
      grid: {
        top: 5,
        bottom: 5,
        left: 5,
        right: 5
      },
      series: [{
        type: 'bar',
        data: data,
        itemStyle: { color: '#409EFF' },
        barWidth: 6
      }]
    }
  }

  chart.setOption(option)
}

// const defaultCover = require('@/assets/images/default-cover.png')


const sources = [
  { value: 'ths', label: '同花顺' },
  { value: 'zc', label: '卓创' },
  { value: 'yy', label: '涌溢' },
  { value: 'bc', label: '百川' },
  { value: 'gl', label: '钢联' },
  { value: 'local', label: '本地' }
]

const getSourceLabel = (value) => {
  const source = sources.find(item => item.value === value)
  return source ? source.label : value
}

const getBackgroundColor = (changeRate) => {
  return changeRate >= 0 ? '#F56C6C' : '#67C23A'
}

</script>

<style scoped >
.indicator-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s;
}

.indicator-card:hover {
  transform: translateY(-5px);
}

.card-content {
  cursor: pointer;
}

.cover-image {
  height: 150px;
  background-size: cover;
  background-position: center;
  position: relative;
}

.new-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--el-color-danger);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: bold;
}

.category-tag {
  position: absolute;
  top: 10px;
  left: 10px;
  /* background-color: var(--el-color-primary); */
  background-color: rgba(15, 25, 214, 0.85);
  color: rgb(255, 255, 255);
  padding: 4px 10px;
  border-radius: 6px;
  font-size: 18px;
  font-weight: bold;
  text-transform: uppercase;
}

.indicator-info {
  padding: 15px;
}

.title {
  margin: 0;
  font-size: 18px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1; /* 占据左侧空间 */
}

.title-wrapper {
  display: flex;
  justify-content: space-between; /* 左右对齐 */
  align-items: center;
  margin: 10px 0 8px 0;
}

.meta-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.author {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.author .author-avatar {
  margin-right: 8px;
}

.author-name {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.time {
  font-size: 14px;
  color: var(--el-text-color-placeholder);
}

.summary {
  margin: 0 0 15px 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-actions {
  display: flex;
  justify-content: space-around;
  padding: 10px 0;
  border-top: 1px solid var(--el-border-color-light);
  background-color: #f9f9f9; /* 浅灰色背景 */
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.card-actions .el-button.favorited {
  color: #F56C6C; /* 已收藏用红色 */
}

.card-actions .el-button:hover {
  background-color: rgba(64, 158, 255, 0.1); /* hover 蓝色透明背景 */
}

.cover-image {
  height: 150px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}

.meta-summary {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 12px;
}

.board-name {
  font-weight: 500;
  color: #333;
}

.divider {
  margin: 0 8px;
  color: var(--el-text-color-placeholder);
}

.product-name {
  color: var(--el-text-color-secondary);
  font-size: 13px;
}

.mini-echart {
  width: 100%;
  height: 150px; /* 必须有明确高度 */
}

.rate-info {
  padding: 8px 15px;
  color: white;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

/* 对 span 中的数字进行样式增强 */
.rate-info span {
  font-weight: bold; /* 加粗 */
  font-size: 18px;   /* 增大字体 */
}
</style>
