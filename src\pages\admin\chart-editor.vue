<template>
  <div class="main-content">
    <div class="line-chart-table-page">
      <!-- 左侧区域 -->
      <div class="left-panel">
        <div class="tag-header">
          <el-tag
            v-for="tag in ['指标库', '我的指标']"
            :key="tag"
            :type="activeTag === tag ? 'primary' : 'info'"
            effect="dark"
            style="margin-right: 8px; cursor: pointer;"
            @click="switchTag(tag)"
          >
            {{ tag }}
          </el-tag>
        </div>

        <!-- 公共的 el-tree -->
        <el-tree
          ref="tree"
          :data="currentTreeData"
          node-key="id"
          default-expand-all
          :props="defaultProps"
          @node-contextmenu="handleLibraryNodeDblclick"
        ></el-tree>
      </div>

      <!-- 右侧主体区域 -->
      <div class="main-content">
        <!-- 图表区域 -->
        <div class="chart-container">
          <div class="chart-header">
            <div>
              <h3 class="chart-title">多指标趋势图</h3>
              <!-- 新增：上传按钮 -->
              <el-button 
                size="small" 
                type="primary" 
                @click="openUploadDialog"
              >
                上传指标
              </el-button>
            </div>
            
            <div class="chart-actions">
              <!-- 原有控件 -->
              <div class="chart-controls">
                <el-button-group>
                  <el-button size="small" @click="toggleShowPoints">
                    {{ showPoints ? '隐藏关键点' : '显示关键点' }}
                  </el-button>
                  <el-button size="small" @click="toggleShowExtremes">
                    {{ showExtremes ? '隐藏高低点' : '显示高低点' }}
                  </el-button>
                  <el-button size="small" @click="resetChart">重置视图</el-button>
                </el-button-group>
              </div>
            </div>
          </div>
          <div ref="chart" style="width: 100%; height: 380px;"></div>
        </div>

        <!-- 表格区域 -->
        <div class="table-container">
          <el-table 
              :data="tableData" 
              border 
              style="width: 100%"
              :row-style="{ height: '48px' }"
              :cell-style="{ padding: '4px 0' }"
              mini-height="350"
              max-height="350"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266', position: 'sticky', top: 0, zIndex: 10 }"
            >
            <!-- 基础信息列 -->
            <el-table-column prop="name" label="指标名称" min-width="160" show-overflow-tooltip />
            <el-table-column prop="id" label="指标ID" min-width="120" />
            <el-table-column prop="frequency" label="频率" min-width="80" />
            <el-table-column prop="unit" label="单位" min-width="80" />
            <el-table-column prop="startDate" label="起始时间" min-width="120" />
            <el-table-column prop="endDate" label="截止时间" min-width="120" />

            <!-- 轴线设置列（下拉选择） -->
            <el-table-column label="轴线设置" min-width="120">
              <template #default="{ row }">
                <el-select 
                  v-model="row.indicatorAxis" 
                  size="small"
                  style="width: 100%"
                  popper-class="axis-select-dropdown"
                  :key="'select-' + row.id"
                  @change="handleAxisChange(row)"
                >
                  <el-option 
                    v-for="item in axisOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
            </el-table-column>

            <!-- 颜色设置列（颜色选择器） -->
            <el-table-column label="颜色设置" min-width="120">
              <template #default="{ row }">
                <el-color-picker 
                  v-model="row.color" 
                  size="small"
                  popper-class="color-picker-dropdown"
                  :predefine="predefineColors"
                  show-alpha
                  :key="'color-picker-' + row.id"
                  @change="handleColorChange(row)"
                  style="width: 100%"
                  :teleported="false"
                  disable-transitions
                />
              </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column label="操作" min-width="140" fixed="right">
              <template #default="{ row }">
                <el-button-group>
                  <el-button 
                    type="primary" 
                    size="small"
                    @click="handleEdit(row)"
                  >
                    编辑
                  </el-button>
                  <el-button 
                    type="danger" 
                    size="small"
                    @click="handleDelete(row)"
                  >
                    删除
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
  <!-- 指标数据上传对话框 -->
  <div>
    <IndicatorUploadDialog
      v-if="visibleUploadDialog"
      :visible="visibleUploadDialog"
      @update:visible="handleDialogUpdate"
    />
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { buildChartOption, updateSeriesMarkPoint, updateSeriesMarkLine } from './chartConfig';
import IndicatorUploadDialog from '~/components/indicator/IndicatorUploadDialog.vue';



export default {
  name: 'LineChartTablePage',
  components: {
    IndicatorUploadDialog,
  },
  data() {
    return {
      // 新增：当前选中的 tag
      activeTag: '指标库',
      visibleUploadDialog: false, 
    
      
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      libraryTreeData: [
        {
          id: 1,
          label: '黑色金属',
          children: [
            { id: 101, label: '钢材' },
            { id: 102, label: '螺纹钢基差' },
            { id: 103, label: '热轧带肋钢筋' },
            { id: 104, label: '铁矿石价格' }
          ]
        },
        {
          id: 2,
          label: '石油化工',
          children: [
            { id: 201, label: '原油期货' },
            { id: 202, label: '成品油价格' },
            { id: 203, label: '聚乙烯(PE)' },
            { id: 204, label: '聚丙烯(PP)' }
          ]
        },
        {
          id: 3,
          label: '有色与新能源',
          children: [
            { id: 301, label: '铜价指数' },
            { id: 302, label: '铝价指数' },
            { id: 303, label: '锂电材料' },
            { id: 304, label: '光伏组件价格' },
            { id: 305, label: '风电叶片原材料' }
          ]
        }
      ],
      myMetricsTreeData: [
        {
          id: 10,
          label: '自定义指标组',
          children: [
            { id: 11, label: '铜价指数' },
            { id: 12, label: '铝价指数' }
          ]
        }
      ],
      chart: null,
      last30Days: [],
      isLast30DaysInitialized: false,
      showPoints: false,
      showExtremes: false,
      axisOptions: [
        { label: '主轴', value: 'primary' },
        { label: '次轴', value: 'secondary' }
      ],
      predefineColors: [
        '#5470c6',
        '#91cc75',
        '#fac858',
        '#ee6666',
        '#73c0de',
        '#3ba272',
        '#fc8452',
        '#9a60b4',
        '#ea7ccc'
      ],
      tableData: [
        {
          name: '铜价前日增长率',
          id: 'metric_001',
          frequency: '日',
          unit: '%',
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          indicatorAxis: 'primary',
          color: '#5470c6'
        },
        {
          name: '铜价',
          id: 'metric_002',
          frequency: '月',
          unit: '元/吨',
          startDate: '2022-01-01',
          endDate: '2023-12-31',
          indicatorAxis: 'secondary',
          color: '#91cc75'
        }
      ]
    };
  },
  computed: {
    currentTreeData() {
      return this.activeTag === '指标库' ? this.libraryTreeData : this.myMetricsTreeData;
    }
  },
  mounted() {
    this.initLast30Days();
    this.initChart();
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    handleDialogUpdate(visible) {
      this.visibleUploadDialog = visible;
    },
    initLast30Days() {
      const dates = [];
      const today = new Date();
      for (let i = 29; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);
        dates.push(date.toISOString().split('T')[0]);
      }
      this.last30Days = dates;
      this.isLast30DaysInitialized = true;
    },
    generateTrendData() {
      return Array(30)
        .fill()
        .map((_, i) => Math.round(500 + Math.random() * 1000 + Math.sin(i / 3) * 200));
    },
    initChart() {
      if (!this.$refs.chart) return;
      if (this.chart) {
        this.chart.dispose();
      }
      this.chart = echarts.init(this.$refs.chart);
      this.updateChart();
    },
    updateChart() {
      const seriesList = this.tableData.map(item => ({
        name: item.name,
        data: this.generateTrendData(),
        color: item.color,
        yAxisIndex: item.indicatorAxis === 'primary' ? 0 : 1
      }));
      const option = buildChartOption(seriesList, this.last30Days);
      this.chart.setOption(option, true);
    },
    toggleShowPoints() {
      this.showPoints = !this.showPoints;
      const seriesList = this.chart.getOption().series;
      seriesList.forEach((series, index) => {
        updateSeriesMarkPoint(this.chart, index, this.showPoints);
      });
    },
    toggleShowExtremes() {
      this.showExtremes = !this.showExtremes;
      const seriesList = this.chart.getOption().series;
      seriesList.forEach((series, index) => {
        updateSeriesMarkLine(this.chart, index, this.showExtremes);
      });
    },
    resetChart() {
      this.showPoints = false;
      this.showExtremes = false;
      this.updateChart();
    },
    // 新增方法：切换 tag
    switchTag(tag) {
      this.activeTag = tag;
      this.currentTreeData = tag === '指标库' ? this.libraryTreeData : this.myMetricsTreeData;
      this.$refs.tree.updateKey(); // 可选：强制刷新树组件
    },
    handleLibraryNodeDblclick(event, data, node) {
      event.preventDefault();

      // 忽略一级节点（只允许二级节点添加）
      if (node.level !== 2) return;

      const category = node.parent.label;
      const metricName = data.label;
      const id = `metric_${Date.now()}`;

      // 构造新指标对象
      const newMetric = {
        name: `${category}-${metricName}`,
        id,
        frequency: '日',
        unit: '元/吨',
        startDate: this.last30Days[0],
        endDate: this.last30Days[this.last30Days.length - 1],
        indicatorAxis: this.tableData.length % 2 === 0 ? 'primary' : 'secondary',
        color: this.predefineColors[this.tableData.length % this.predefineColors.length]
      };

      // ✅ 判断是否已存在该指标（根据 name 或 id 判断）
      const isExist = this.tableData.some(item => item.name === newMetric.name);

      if (!isExist) {
        this.tableData.push(newMetric);
        this.$nextTick(() => {
          this.updateChart();
        });
      } else {
        this.$message.warning('该指标已存在，不可重复添加');
      }
    },
    handleAxisChange(row) {
      this.$nextTick(() => {
        this.updateChart();
      });
    },
    handleColorChange(row) {
      this.$nextTick(() => {
        this.updateChart();
      });
    },
    handleEdit(row) {
      this.$message.info(`正在编辑：${row.name}`);
    },
    handleDelete(row) {
      this.$confirm(`确认删除指标 "${row.name}"?`, '提示', { type: 'warning' }).then(() => {
        this.tableData = this.tableData.filter(item => item.id !== row.id);
        this.updateChart();
        this.$message.success('删除成功');
      });
    },
    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
    },
    openUploadDialog() {
      this.visibleUploadDialog = true;
    },
  }
};
</script>

<style scoped lang="scss">
.tag-header {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
  background-color: #f9fafc;
  border-radius: 4px;
}

.line-chart-table-page {
  display: flex;
  height: 100%;
  padding: 16px;

  .left-panel {
    width: 300px;
    margin-right: 16px;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    overflow: hidden;

    .el-tabs--card > .el-tabs__header {
      margin-bottom: 0;
    }

    .el-tree {
      padding: 8px;
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative; // 必须设置以支持 z-index
    z-index: 1;         // 保证主内容在基础层
  }

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 10px;
    padding: 10px 10px;
    border-bottom: 1px solid #dcdfe6;
    box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.05);

    .chart-header > div {
      display: inline-block;
      align-items: center;
    }

    .chart-title {
      margin-right: 12px;
      font-size: 16px;
      font-weight: 500;
    }


    .chart-controls {
      .el-button-group {
        margin-left: 10px;
      }
    }
  }

  .chart-container {
    flex: 0 0 400px;
    margin-bottom: 16px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }

  .table-container {
   
  }
}

.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  pointer-events: auto;
}
</style>