<template>
  <div class="report-entry-container">
    <el-row :gutter="20">
      <!-- 左侧菜单 -->
      <el-col :span="2">
        <el-menu
          :default-active="activeMenu"
          :default-openeds="['2']"
          @select="handleMenuSelect"
          class="menu-style"
          unique-opened
        >
          <template v-for="item in menuItems" :key="item.id">
            <el-menu-item v-if="!item.children" :index="item.id">
              <span>{{ item.label }}</span>
            </el-menu-item>

            <el-sub-menu v-else :index="item.id" :key="item.id">
              <template #title>
                <span>{{ item.label }}</span>
              </template>

              <el-menu-item
                v-for="child in item.children"
                :key="child.id"
                :index="child.id"
              >
                <span style="margin-left: 10px;">{{ child.label }}</span>
              </el-menu-item>
            </el-sub-menu>
          </template>
        </el-menu>
      </el-col>

      <!-- 右侧主内容 -->
      <el-col :span="22">
        <!-- 查询区域 -->
        <el-form
          :inline="true"
          :model="searchForm"
          label-width="80px"
          label-position="right"
          class="search-form"
        >
          <el-row :gutter="24">
            <!-- 查询条件 -->
            <el-col :span="18">
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item label="报告名称">
                    <el-input v-model="searchForm.reportName" placeholder="请输入报告名称" />
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="作者">
                    <el-input v-model="searchForm.author" placeholder="请输入作者名称" />
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="报告类型">
                    <el-select v-model="searchForm.reportType" placeholder="请选择" style="width: 100%">
                      <el-option label="日报" value="日报" />
                      <el-option label="周报" value="周报" />
                      <el-option label="月报" value="月报" />
                      <el-option label="年报" value="年报" />
                      <el-option label="专题" value="专题" />
                      <el-option label="策略" value="策略" />
                      <el-option label="调研" value="调研" />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="发布时间">
                    <el-date-picker
                      v-model="searchForm.dateRange"
                      type="daterange"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="品种板块">
                    <el-select v-model="searchForm.productCategory" placeholder="请选择" style="width: 100%">
                      <el-option label="能化" value="能化" />
                      <el-option label="有色" value="有色" />
                      <el-option label="黑色" value="黑色" />
                      <el-option label="农产品" value="农产品" />
                      <el-option label="金融" value="金融" />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="品种类别">
                    <el-input v-model="searchForm.productType" placeholder="请输入品种类别" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <!-- 按钮区域 -->
            <el-col :span="6">
              <div class="action-buttons">
                <el-button type="primary" @click="onSearch" style="width: 120px;">查询</el-button>
              </div>
            </el-col>
          </el-row>
        </el-form>

        <!-- 表格 -->
        <el-table :data="paginatedData" border style="width: 100%" v-loading="loading">
          <el-table-column prop="id" label="ID" width="60" align="center" />
          <el-table-column prop="reportName" label="报告名称" />
          <el-table-column prop="contractName" label="合约名称" width="120" align="center" />
          <el-table-column prop="reportType" label="报告类型" width="120" align="center" />
          <el-table-column prop="submitDate" label="提交时间" width="160" align="center" />
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === '已提交' ? 'success' : 'info'">{{ row.status }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" show-overflow-tooltip />
          <el-table-column label="操作" width="150" align="center">
            <template #default="{ $index }">
              <el-button size="small" @click="handleEdit($index)">编辑</el-button>
              <el-button size="small" type="danger" @click="handleDelete($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination
          layout="prev, pager, next"
          :total="tableData.length"
          :page-size="pageSize"
          :current-page="currentPage"
          @current-change="handlePageChange"
          style="margin-top: 20px; text-align: center"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  Postcard,
  DocumentAdd
} from '@element-plus/icons-vue'

const router = useRouter()

// 当前激活的菜单项
const activeMenu = ref('2-1')

const availableIcons = [Postcard, DocumentAdd]

// 菜单数据
const menuItems = ref([
  {
    id: '1',
    label: '研报列表',
    icon: availableIcons[0]
  },
  {
    id: '2',
    label: '研报管理',
    icon: availableIcons[1],
    children: [
      { id: '2-1', label: '上传研报' },
      { id: '2-2', label: '系统制作研报' }
    ]
  }
])

// 菜单点击事件
const handleMenuSelect = (index: string) => {
  if (index === '1') {
    router.push('/report')
  } else if (index === '2-1') {
    router.push('/report-manage')
  } else if (index === '2-2') {
    router.push('/report-editor')
  }
}

// 查询条件
const searchForm = ref({
  reportName: '',
  author: '',
  reportType: '', // ✅ 初始化为空字符串
  dateRange: [],
  productCategory: '',
  productType: ''
})

// 表格模拟数据
const tableData = ref([
  { id: 1, reportName: '黄金期货分析', contractName: 'AU2406', reportType: '周报', submitDate: '2024-04-01', status: '已提交', remark: '基于K线形态的技术分析', author: '张三', productCategory: '有色', productType: '黄金' },
  { id: 2, reportName: '原油期货展望', contractName: 'CL2407', reportType: '日报', submitDate: '2024-04-02', status: '草稿', remark: '未完成', author: '李四', productCategory: '能化', productType: '原油' },
  { id: 3, reportName: '大豆期货周报', contractName: 'S2408', reportType: '周报', submitDate: '2024-04-03', status: '已提交', remark: '', author: '王五', productCategory: '农产品', productType: '大豆' },
  { id: 4, reportName: '铜期货短期策略', contractName: 'CU2409', reportType: '策略', submitDate: '2024-04-04', status: '草稿', remark: '测试数据', author: '赵六', productCategory: '有色', productType: '铜' },
  { id: 5, reportName: '天然气期货分析', contractName: 'NG2410', reportType: '日报', submitDate: '2024-04-05', status: '已提交', remark: '完整版报告', author: '孙七', productCategory: '能化', productType: '天然气' },
  { id: 6, reportName: '棉花期货展望', contractName: 'CT2411', reportType: '调研', submitDate: '2024-04-06', status: '草稿', remark: '', author: '周八', productCategory: '农产品', productType: '棉花' },
  { id: 7, reportName: '白银期货周报', contractName: 'AG2412', reportType: '周报', submitDate: '2024-04-07', status: '已提交', remark: '最新市场动态', author: '吴九', productCategory: '有色', productType: '白银' },
  { id: 8, reportName: '小麦期货策略', contractName: 'W2501', reportType: '策略', submitDate: '2024-04-08', status: '草稿', remark: '待补充详细策略', author: '郑十', productCategory: '农产品', productType: '小麦' }
])

// 分页配置
const pageSize = 6
const currentPage = ref(1)
const loading = ref(false)

// 分页计算
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  return tableData.value.slice(start, start + pageSize)
})

// 分页事件
const handlePageChange = (page: number) => {
  currentPage.value = page
}

// 查询逻辑（可扩展）
const onSearch = () => {
  console.log('执行搜索:', searchForm.value)
  currentPage.value = 1
}

// 重置逻辑
const onReset = () => {
  searchForm.value = {
    reportName: '',
    author: '',
    reportType: '',
    dateRange: [],
    productCategory: '',
    productType: ''
  }

  tableData.value = [
    { id: 1, reportName: '黄金期货分析', contractName: 'AU2406', reportType: '周报', submitDate: '2024-04-01', status: '已提交', remark: '基于K线形态的技术分析', author: '张三', productCategory: '有色', productType: '黄金' },
    { id: 2, reportName: '原油期货展望', contractName: 'CL2407', reportType: '日报', submitDate: '2024-04-02', status: '草稿', remark: '未完成', author: '李四', productCategory: '能化', productType: '原油' },
    { id: 3, reportName: '大豆期货周报', contractName: 'S2408', reportType: '周报', submitDate: '2024-04-03', status: '已提交', remark: '', author: '王五', productCategory: '农产品', productType: '大豆' },
    { id: 4, reportName: '铜期货短期策略', contractName: 'CU2409', reportType: '策略', submitDate: '2024-04-04', status: '草稿', remark: '测试数据', author: '赵六', productCategory: '有色', productType: '铜' },
    { id: 5, reportName: '天然气期货分析', contractName: 'NG2410', reportType: '日报', submitDate: '2024-04-05', status: '已提交', remark: '完整版报告', author: '孙七', productCategory: '能化', productType: '天然气' },
    { id: 6, reportName: '棉花期货展望', contractName: 'CT2411', reportType: '调研', submitDate: '2024-04-06', status: '草稿', remark: '', author: '周八', productCategory: '农产品', productType: '棉花' },
    { id: 7, reportName: '白银期货周报', contractName: 'AG2412', reportType: '周报', submitDate: '2024-04-07', status: '已提交', remark: '最新市场动态', author: '吴九', productCategory: '有色', productType: '白银' },
    { id: 8, reportName: '小麦期货策略', contractName: 'W2501', reportType: '策略', submitDate: '2024-04-08', status: '草稿', remark: '待补充详细策略', author: '郑十', productCategory: '农产品', productType: '小麦' }
  ]

  currentPage.value = 1
}

// 编辑/删除方法
const handleEdit = (index: number) => alert(`编辑第 ${index + 1} 行`)
const handleDelete = (index: number) => tableData.value.splice(index, 1)
</script>

<style scoped lang="scss">
.report-entry-container {
  padding: 20px;
}

.search-form {
  .el-form-item__label {
    width: 80px !important;
    // text-align: right;
    padding-right: 10px;
  }

  .el-form-item__content {
    width: calc(100% - 90px); // label 宽度 + 间距
    display: flex;
    align-items: center;
  }

  .el-input,
  .el-select,
  .el-date-editor {
    width: 100%;
  }

  .el-select .el-input__inner {
    width: 100%;
  }

  .el-form-item {
    margin-bottom: 16px;
  }
}

.action-buttons {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
  height: 100%;
}

// 设置按钮宽度
.action-buttons .el-button {
  width: 120px;
}

.menu-style {
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #f9f9f9;
  padding: 10px 0;
}

// ::v-deep 用于穿透 scoped，解决 el-select 文字不可见问题
::v-deep .search-form {
  .el-select {
    width: 100%;

    .el-input__inner {
      color: #606266 !important;
      height: 32px;
      line-height: 32px;
      padding-right: 30px;
      width: 100%;
    }

    .el-select__placeholder {
      color: #999;
    }
  }
}
</style>