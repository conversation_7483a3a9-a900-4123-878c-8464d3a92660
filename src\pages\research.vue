<template>
  <!-- <SubHeader /> -->
  <div class="inventory-chart">
    <div v-if="showCharts" class="chart-container">
      <el-row :gutter="20">
        <!-- 左侧菜单 -->
        <el-col :span="3">
          <!-- <el-menu
            :default-active="activeMenu"
            @select="handleMenuSelect"
            class="menu-style"
          >
            <el-menu-item
              v-for="(item, index) in menuItems"
              :key="index"
              :index="item.id.toString()"
            >
              <el-icon><component :is="item.icon" /></el-icon>
              <span style="margin-left: 10px;">{{ item.label }}</span>
            </el-menu-item>
          </el-menu> -->
          <el-menu
            :default-active="activeMenu"
            @select="handleMenuSelect"
            class="menu-style"
            unique-opened
          >
            <!-- 一级菜单 -->
            <el-sub-menu index="1" title="研报中心">
              <template #title>
                <span>研究分项数据图表制作</span>
              </template>
              <!-- 二级菜单1 -->
              <el-sub-menu index="1-1" title="二级菜单1">
                <template #title>
                  <span>逻辑推演</span>
                </template>
                <el-menu-item index="1-1-1">日报</el-menu-item>
                <el-menu-item index="1-1-2">周报</el-menu-item>
              </el-sub-menu>

              <!-- 二级菜单2 -->
              <el-sub-menu index="1-2" title="二级菜单2">
                <template #title>
                  <span>品种研报</span>
                </template>
                <el-menu-item index="1-2-1">日报</el-menu-item>
                <el-menu-item index="1-2-2">周报</el-menu-item>
              </el-sub-menu>
            </el-sub-menu>
            <el-sub-menu index="2" title="研报中心">
              <template #title>
                <span>研究分项逻辑推演制作</span>
              </template>
              <!-- 二级菜单1 -->
              <el-sub-menu index="2-1" title="二级菜单1">
                <template #title>
                  <span>逻辑推演</span>
                </template>
                <el-menu-item index="2-1-1">日报</el-menu-item>
                <el-menu-item index="2-1-2">周报</el-menu-item>
              </el-sub-menu>

              <!-- 二级菜单2 -->
              <el-sub-menu index="2-2" title="二级菜单2">
                <template #title>
                  <span>品种研报</span>
                </template>
                <el-menu-item index="2-2-1">日报</el-menu-item>
                <el-menu-item index="2-2-2">周报</el-menu-item>
              </el-sub-menu>
            </el-sub-menu>
          </el-menu>
        </el-col>

        <!-- 右侧图表容器 -->
        <el-col :span="21">
          <el-row :gutter="22">
            <el-col
              v-for="(chart, index) in paginatedCharts"
              :key="index"
              :span="12"
              class="chart-item"
            >
              <el-card shadow="hover" class="chart-card">
                <template #header>
                  <div class="card-header">
                    <span>{{ chart.title }}</span>
                    <el-button type="text" @click.stop="openFullScreenChart(chart)">
                      <el-icon><FullScreen /></el-icon>
                    </el-button>
                  </div>
                </template>
                <div :id="'chart-' + index" class="echart" style="width: 100%; height: 450px;"></div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 分页组件 -->
          <el-pagination
            layout="prev, pager, next"
            :total="charts.length"
            :page-size="pageSize"
            @current-change="handlePageChange"
            style="margin-top: 20px; text-align: center"
          />
        </el-col>
      </el-row>
    </div>
  </div>

  <!-- 全屏图表弹窗 -->
  <el-dialog v-model="isDialogVisible" title="详细图表" width="80%">
    <div id="full-chart" class="full-chart" style="width: 100%; height: 600px;"></div>
  </el-dialog>
</template>

<script setup>
import {
  FullScreen,
  Postcard,
  Notebook,
  Files
} from '@element-plus/icons-vue'
import { ref, onMounted, computed } from 'vue'
import * as echarts from 'echarts'

import SubHeader from '~/components/layouts/SubHeader.vue'

const activeMenu = ref('1') // 默认激活的菜单项

// 菜单数据
// 定义一组可用的图标
const availableIcons = [
  Postcard,
  Notebook,
  Files
]

// const menuItems = ref([
//   { id: 1, label: '我的看板', icon: availableIcons[Math.floor(Math.random() * availableIcons.length)] },
//   { id: 2, label: '共享看板', icon: availableIcons[Math.floor(Math.random() * availableIcons.length)] },
//   { id: 3, label: '研究看板', icon: availableIcons[Math.floor(Math.random() * availableIcons.length)] }
// ])
const menuItems = ref([
  { id: 1, label: '研究分项数据图表制作', icon: availableIcons[Math.floor(Math.random() * availableIcons.length)] },
  { id: 2, label: '研究分项逻辑推演制作', icon: availableIcons[Math.floor(Math.random() * availableIcons.length)] }
])

// 菜单选择事件处理
// 菜单选择事件处理
const handleMenuSelect = (index) => {
  console.log('选中的菜单项:', index)

  // 重新生成随机数据
  charts.value = []
  for (let i = 0; i < 12; i++) {
    const seriesData = []

    years.forEach(year => {
      const data = []
      for (let month = 0; month < 12; month++) {
        const value = Math.floor(Math.random() * 500) + 100
        data.push(value)
      }

      seriesData.push({
        name: `${year}年`,
        type: 'line',
        data: data,
        smooth: true,
        showSymbol: false
      })
    })

    charts.value.push({
      title: `库存-${i + 1}`,
      series: seriesData
    })
  }

  // 重新加载图表
  loadCharts()
}

const showCharts = ref(false)
const charts = ref([])

const currentPage = ref(1)
const pageSize = 6

// 计算当前页显示的图表
const paginatedCharts = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  return charts.value.slice(start, start + pageSize)
})

// 切换页码时重新渲染图表
const handlePageChange = (page) => {
  currentPage.value = page
  setTimeout(() => {
    loadCharts()
  }, 50)
}

// 获取最近8年
function getLastNYears(n) {
  const years = []
  const currentYear = new Date().getFullYear()
  for (let i = n - 1; i >= 0; i--) {
    years.push(currentYear - i)
  }
  return years
}

const years = getLastNYears(8)

for (let i = 0; i < 12; i++) {
  const seriesData = []

  // 每年一条线，12个月
  years.forEach(year => {
    const data = []
    for (let month = 0; month < 12; month++) {
      const value = Math.floor(Math.random() * 500) + 100
      data.push(value)
    }

    seriesData.push({
      name: `${year}年`,
      type: 'line',
      data: data,
      smooth: true,
      showSymbol: false
    })
  })

  charts.value.push({
    title: `库存-${i + 1}`,
    series: seriesData
  })
}

const loadCharts = () => {
  showCharts.value = true

  setTimeout(() => {
    charts.value.forEach((_, index) => {
      const chartDom = document.getElementById('chart-' + index)
      if (!chartDom) return

      const myChart = echarts.init(chartDom)

      // 显示加载动画
      myChart.showLoading({
        text: '加载中...',
        color: '#409EFF',
        textColor: '#666',
        maskColor: 'rgba(255, 255, 255, 0.8)'
      })

      // 构建配置项
      const option = {
        title: {
          text: _.title,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let result = params[0].name + '<br/>'
            params.forEach(p => {
              result += `${p.marker} ${p.seriesName}: ${p.value}<br/>`
            })
            return result
          }
        },
        grid: {
          top: 80,
          bottom: 40,
          left: 60,
          right: 20
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          name: '月份'
        },
        yAxis: {
          type: 'value',
          name: '库存量',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#666'
            }
          }
        },
        legend: {
          data: _.series.map(s => s.name),
          top: 30,
          left: 'center',
          bottom: 10
        },
        series: _.series
      }

      // 设置配置并隐藏 loading
      setTimeout(() => {
        myChart.hideLoading()
        myChart.setOption(option)
      }, 800)
    })
  }, 0)
}

onMounted(() => {
  loadCharts()
})

// 弹窗相关逻辑
const isDialogVisible = ref(false)
let fullChart = null

const openFullScreenChart = (chartData) => {
  isDialogVisible.value = true

  setTimeout(() => {
    if (!fullChart) {
      fullChart = echarts.init(document.getElementById('full-chart'))
    }

    const option = {
      title: {
        text: chartData.title,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          let result = params[0].name + '<br/>'
          params.forEach(p => {
            result += `${p.marker} ${p.seriesName}: ${p.value}<br/>`
          })
          return result
        }
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        name: '月份'
      },
      yAxis: {
        type: 'value',
        name: '库存量',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#666'
          }
        }
      },
      legend: {
        data: chartData.series.map(s => s.name),
        top: 30,
        left: 'center',
        bottom: 10
      },
      series: chartData.series
    }

    fullChart.setOption(option)
  }, 0)
}
</script>

<style scoped lang="scss">
.inventory-chart {
  padding: 20px;
}

.chart-card {
  margin-bottom: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.menu-style {
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #f9f9f9;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>