import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios';

// 创建axios实例
const service: AxiosInstance = axios.create({
//   baseURL: process.env.VUE_APP_BASE_API, // api的base_url
  baseURL: import.meta.env.VITE_API_PREFIX ?? import.meta.env.VITE_API_BASE_URL,
//   baseURL: '/investment-api',
  timeout: 5000,
});

// request拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 添加headers类型断言
    config.headers = config.headers || {};
    // 可添加默认请求头
    // config.headers['Content-Type'] = 'application/json';
    // config.headers['Content-Type'] = 'application/json;charset=utf-8';
    // config.headers['Accept'] = 'application/json, text/plain, */*';
    return config;
  },
  (error: any) => {
    Promise.reject(error);
  }
);

// response拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data;
  },
  (error: any) => {
    return Promise.reject(error);
  }
);

service.interceptors.request.use(
  (config) => {
    console.log('请求配置:', {
      url: config.url,
      baseURL: config.baseURL,
      //fullUrl: config.baseURL + config.url,
      headers: config.headers,
    });
    return config;
  },
  (error) => {
    console.error('请求拦截错误:', error);
    return Promise.reject(error);
  }
);

export default service;