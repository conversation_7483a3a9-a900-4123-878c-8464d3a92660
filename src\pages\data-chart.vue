<template>
  <!-- <SubHeader /> -->
  <div class="inventory-chart">
    <div v-if="showCharts" class="chart-container">
      <el-row :gutter="20">
        <!-- 左侧菜单 -->
        <el-col :span="3">
            <el-menu
                :default-active="activeMenu"
                @select="handleMenuSelect"
                class="menu-style"
                unique-opened
                >
                <template v-for="item in menuItems" :key="item.id">
                    <!-- 如果没有子菜单，则直接显示一级菜单项 -->
                    <el-menu-item v-if="!item.children" :index="item.id">
                    <span>{{ item.label }}</span>
                    </el-menu-item>

                    <!-- 如果有子菜单，则显示为 el-sub-menu -->
                    <el-sub-menu v-else :index="item.id">
                    <template #title>
                        <span>{{ item.label }}</span>
                    </template>

                    <!-- 遍历二级菜单 -->
                    <el-menu-item
                        v-for="child in item.children"
                        :key="child.id"
                        :index="child.id"
                    >
                        <span style="margin-left: 10px;">{{ child.label }}</span>
                    </el-menu-item>
                    </el-sub-menu>
                </template>
            </el-menu>
        </el-col>

        <!-- 右侧图表容器 -->
        <el-col :span="21">
          <el-row :gutter="22">
            <el-col
              v-for="(chart, index) in paginatedCharts"
              :key="index"
              :span="8"
              class="chart-item"
            >
              <el-card shadow="hover" class="chart-card">
                <template #header>
                  <div class="card-header">
                    <span>{{ chart.title }}</span>
                    <el-button type="text" @click.stop="openFullScreenChart(chart)">
                      <el-icon><FullScreen /></el-icon>
                    </el-button>
                  </div>
                </template>
                <div :id="'chart-' + index" class="echart" style="width: 100%; height: 450px;"></div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 分页组件 -->
          <el-pagination
            layout="prev, pager, next"
            :total="charts.length"
            :page-size="pageSize"
            @current-change="handlePageChange"
            style="margin-top: 20px; text-align: center"
          />
        </el-col>
      </el-row>
    </div>
  </div>

  <!-- 全屏图表弹窗 -->
  <el-dialog v-model="isDialogVisible" title="详细图表" width="80%">
    <div id="full-chart" class="full-chart" style="width: 100%; height: 600px;"></div>
  </el-dialog>
</template>

<script setup lang="ts">
import {
  FullScreen,
  Postcard,
  Notebook,
  Files
} from '@element-plus/icons-vue'
import { ref, onMounted, computed } from 'vue'
import * as echarts from 'echarts'
import { useRouter } from 'vue-router'

import SubHeader from '~/components/layouts/SubHeader.vue'

const router = useRouter()

const activeMenu = ref('2') // 默认激活的菜单项

// 菜单数据
// 定义一组可用的图标
const availableIcons = [
  Postcard,
  Notebook,
  Files
]

// const menuItems = ref([
//   { id: 1, label: '我的看板', icon: availableIcons[Math.floor(Math.random() * availableIcons.length)] },
//   { id: 2, label: '共享看板', icon: availableIcons[Math.floor(Math.random() * availableIcons.length)] },
//   { id: 3, label: '研究看板', icon: availableIcons[Math.floor(Math.random() * availableIcons.length)] }
// ])
const menuItems = ref([
  {
    id: '0',
    label: '逻辑推演'
  },
  {
    id: '1',
    label: '研报列表',
    children: [
      { id: '2-1', label: '周报' },
      { id: '2-2', label: '月报' },
      { id: '2-3', label: '年报' },
      { id: '2-4', label: '专题' },
      { id: '2-5', label: '调研' },
      { id: '2-6', label: '策略' }
    ]
  },
  {
    id: '2',
    label: '数据图表'
  }
])
// 菜单选择事件处理
const handleMenuSelect = (index: string) => {
  console.log('选中的菜单项:', index)

  if (index === '0') {
    router.push('/logical-deduction')
  } else if (index === '1') {
    router.push('/report')
  } else if (index.startsWith('2-')) {
    router.push('/report')
  } else if (index === '2') {
    router.push('/data-chart')
  }
}

interface SeriesDataItem {
  name: string;
  type: string;
  data: number[];
  smooth: boolean;
  showSymbol: boolean;
}

interface ChartItem {
  title: string;
  series: SeriesDataItem[]; // 可以根据需要进一步定义series的具体结构
}

const showCharts = ref(false)
const charts = ref<ChartItem[]>([])

const currentPage = ref(1)
const pageSize = 6

// 计算当前页显示的图表
const paginatedCharts = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  return charts.value.slice(start, start + pageSize)
})

// 切换页码时重新渲染图表
const handlePageChange = (page: number) => {
  currentPage.value = page
  setTimeout(() => {
    loadCharts()
  }, 50)
}

// 获取最近8年
function getLastNYears(n: number) {
  const years = []
  const currentYear = new Date().getFullYear()
  for (let i = n - 1; i >= 0; i--) {
    years.push(currentYear - i)
  }
  return years
}

const years = getLastNYears(8)

for (let i = 0; i < 12; i++) {
  const seriesData: SeriesDataItem[] = []

  // 每年一条线，12个月
  years.forEach(year => {
    const data = []
    let baseValue = Math.floor(Math.random() * 500) + 100; // 基础值
    for (let month = 0; month < 12; month++) {
      const fluctuation = (Math.random() - 0.5) * 40; // 波动值
      baseValue += fluctuation; // 累加波动值以形成趋势
      data.push(Math.max(baseValue, 50)); // 确保值不小于50
    }

    seriesData.push({
      name: `${year}年`,
      type: 'line',
      data: data,
      smooth: true,
      showSymbol: false
    })
  })

  charts.value.push({
    title: `库存-${i + 1}`,
    series: seriesData
  })
}

const loadCharts = () => {
  showCharts.value = true

  setTimeout(() => {
    charts.value.forEach((_, index) => {
      const chartDom = document.getElementById('chart-' + index)
      if (!chartDom) return

      const myChart = echarts.init(chartDom)

      // 显示加载动画
      myChart.showLoading({
        text: '加载中...',
        color: '#409EFF',
        textColor: '#666',
        maskColor: 'rgba(255, 255, 255, 0.8)'
      })

      // 构建配置项
      const option = {
        title: {
          text: _.title,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params: any[]) => {
            let result = params[0].name + '<br/>'
            params.forEach((p: any) => {
              result += `${p.marker} ${p.seriesName}: ${p.value}<br/>`
            })
            return result
          }
        },
        grid: {
          top: 80,
          bottom: 40,
          left: 60,
          right: 20
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          name: '月份'
        },
        yAxis: {
          type: 'value',
          name: '库存量',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#666'
            }
          }
        },
        legend: {
          data: _.series.map(s => s.name),
          top: 30,
          left: 'center',
          bottom: 10
        },
        series: _.series
      }

      // 设置配置并隐藏 loading
      setTimeout(() => {
        myChart.hideLoading()
        myChart.setOption(option)
      }, 800)
    })
  }, 0)
}

onMounted(() => {
  loadCharts()
})

// 弹窗相关逻辑
const isDialogVisible = ref(false)
let fullChart: echarts.ECharts | null = null

const openFullScreenChart = (chartData: ChartItem) => {
  isDialogVisible.value = true

  setTimeout(() => {
    if (!fullChart) {
      fullChart = echarts.init(document.getElementById('full-chart'))
    }

    const option = {
      title: {
        text: chartData.title,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any[]) => {
          let result = params[0].name + '<br/>'
          params.forEach(p => {
            result += `${p.marker} ${p.seriesName}: ${p.value}<br/>`
          })
          return result
        }
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        name: '月份'
      },
      yAxis: {
        type: 'value',
        name: '库存量',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#666'
          }
        }
      },
      legend: {
        data: chartData.series.map((s: SeriesDataItem) => s.name),
        top: 30,
        left: 'center',
        bottom: 10
      },
      series: chartData.series
    }

    fullChart.setOption(option)
  }, 0)
}
</script>

<style scoped lang="scss">
.inventory-chart {
  padding: 20px;
}

.chart-card {
  margin-bottom: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.menu-style {
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #f9f9f9;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-item {
  
}
</style>