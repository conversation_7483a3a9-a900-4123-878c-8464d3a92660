// src/pages/admin/chartConfig.js
export function buildChartOption(seriesList, xAxisData) {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: seriesList.map(s => s.name),
        top: 10,
        type: 'scroll'
      },
      grid: {
        top: 40,
        right: '3%',
        left: '3%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xAxisData,
        axisLabel: {
          formatter(value, index) {
            return index % 7 === 0 ? value.substring(5, 10) : '';
          }
        }
      },
      yAxis: [
        { type: 'value', name: '主轴', position: 'left' },
        { type: 'value', name: '次轴', position: 'right' }
      ],
      series: seriesList.map(item => ({
        name: item.name,
        type: 'line',
        data: item.data,
        smooth: true,
        lineStyle: { width: 2, color: item.color },
        itemStyle: { color: item.color },
        yAxisIndex: item.yAxisIndex,
        connectNulls: true
      }))
    };
  }
  
  export function updateSeriesMarkPoint(chart, index, enabled) {
    const series = chart.getOption().series[index];
    if (enabled) {
      chart.setOption({
        series: [
          {
            index,
            markPoint: {
              data: [
                { type: 'max', name: '最大值' },
                { type: 'min', name: '最小值' }
              ],
              symbolSize: 20,
              label: {
                show: true,
                position: 'top'
              }
            },
            symbolSize: 6
          }
        ]
      });
    } else {
      chart.setOption({
        series: [{ index, markPoint: undefined, symbolSize: 0 }]
      });
    }
  }
  
  export function updateSeriesMarkLine(chart, index, enabled) {
    const series = chart.getOption().series[index];
    const data = series.data || [];
  
    if (enabled) {
      const maxVal = Math.max(...data);
      const minVal = Math.min(...data);
  
      chart.setOption({
        series: [
          {
            index,
            markLine: {
              silent: true,
              data: [
                { type: 'max', name: '最高值' },
                { type: 'min', name: '最低值' }
              ],
              symbol: ['none', 'none'],
              lineStyle: {
                color: series.lineStyle?.color,
                width: 1,
                type: 'dashed'
              }
            }
          }
        ]
      });
    } else {
      chart.setOption({
        series: [{ index, markLine: undefined }]
      });
    }
  }