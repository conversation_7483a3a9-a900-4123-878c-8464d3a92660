<template>
  <div class="user-management">
    <!-- 新增：左侧菜单区域 -->
    <el-row :gutter="20">
      <!-- 左侧菜单 -->
      <el-col :span="3">
        <el-menu
          :default-active="activeMenu"
          @select="handleMenuSelect"
          class="menu-style"
          background-color="#f9f9f9"
          text-color="#333"
          active-text-color="#409EFF"
        >
          <el-menu-item v-for="(item, index) in menuItems" :key="index" :index="item.id.toString()">
            <el-icon><component :is="item.icon" /></el-icon>
            <span style="margin-left: 10px;">{{ item.label }}</span>
          </el-menu-item>
        </el-menu>
      </el-col>

      <!-- 原有主内容区域包裹在 el-row 中 -->
      <el-col :span="21">
        <div class="role-management">
          <h2>角色管理</h2>

          <!-- 查询区域 -->
          <el-card class="query-card">
            <el-form :model="queryForm" class="query-form" label-width="140px" label-position="left">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="角色编码">
                    <el-input v-model="queryForm.roleCode" placeholder="请输入角色编码" clearable />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="角色名称">
                    <el-input v-model="queryForm.roleName" placeholder="请输入角色名称" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <div class="button-group">
                    <el-button type="primary" @click="handleQuery">查询</el-button>
                    <el-button @click="resetQuery">重置</el-button>
                    <el-button type="success" @click="handleAdd">新增</el-button>
                    <el-button type="warning" @click="handleExport">导出</el-button>
                  </div>
                </el-col>
              </el-row>
            </el-form>
          </el-card>

          <!-- 表格区域 -->
          <el-card class="table-card">
            <el-table :data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)" border style="width: 100%">
              <el-table-column prop="roleCode" label="角色编码" width="150" />
              <el-table-column prop="roleName" label="角色名称" width="200" />
              <el-table-column prop="roleDescription" label="角色说明" />
              <el-table-column label="操作" width="250" align="center" fixed="right">
                <template #default="scope">
                  <el-button type="primary" size="small" @click="handleEdit(scope.row)">修改</el-button>
                  <el-button type="warning" size="small" @click="handleMenuPermission(scope.row)">菜单权限</el-button>
                  <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="tableData.length"
                :pager-count="7"
              />
            </div>
          </el-card>

          <!-- 新增/修改角色对话框 -->
          <el-dialog
            v-model="dialogVisible"
            :title="dialogTitle"
            width="500px"
            @close="handleDialogClose"
          >
            <el-form
              ref="formRef"
              :model="formData"
              :rules="formRules"
              label-width="100px"
            >
              <el-form-item label="角色编码" prop="roleCode">
                <el-input
                  v-model="formData.roleCode"
                  placeholder="系统自动生成"
                  :disabled="true"
                />
              </el-form-item>
              <el-form-item label="角色名称" prop="roleName">
                <el-input v-model="formData.roleName" placeholder="请输入角色名称" />
              </el-form-item>
              <el-form-item label="角色说明" prop="roleDescription">
                <el-input
                  v-model="formData.roleDescription"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入角色说明"
                />
              </el-form-item>
            </el-form>
            <template #footer>
              <div class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
                <el-button type="primary" @click="handleSubmit" :disabled="!formData.roleName">保存</el-button>
              </div>
            </template>
          </el-dialog>

          <!-- 菜单配置对话框 -->
          <el-dialog
            v-model="menuPermissionDialogVisible"
            title="菜单配置"
            width="600px"
          >
            <el-tree
              ref="menuTreeRef"
              :data="menuTreeData"
              :props="{ label: 'menuName', children: 'children' }"
              show-checkbox
              node-key="id"
              :default-checked-keys="checkedMenus"
              default-expand-all
            >
              <template #default="{ data }">
                <div class="menu-tree-node">
                  <span class="menu-name">{{ data.menuName }}</span>
                  <span class="menu-description">{{ data.menuDescription }}</span>
                </div>
              </template>
            </el-tree>
            <template #footer>
              <div class="dialog-footer">
                <el-button @click="menuPermissionDialogVisible = false">关闭</el-button>
                <el-button type="primary" @click="handleMenuPermissionSubmit">保存</el-button>
              </div>
            </template>
          </el-dialog>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'

import {
  User,
  Key,
  Menu as IconMenu
} from '@element-plus/icons-vue'

const activeMenu = ref('2')

const menuItems = ref([
  {
    id: 1,
    label: '用户管理',
    icon: User
  },
  {
    id: 2,
    label: '角色管理',
    icon: Key
  },
  {
    id: 3,
    label: '菜单管理',
    icon: IconMenu
  }
])

const router = useRouter()
// 菜单选择事件处理
const handleMenuSelect = (index) => {
  console.log('选中的菜单项:', index)

  if (index === '1') {
    router.push('/admin/user-management')
  } else if (index === '2') {
    router.push('/admin/role-management')
  } else if (index === '3') {
    router.push('/admin/menu-management')
  } 
}

// 查询表单
const queryForm = reactive({
  roleCode: '',
  roleName: ''
})

// 分页
const currentPage = ref(2)
const pageSize = ref(10)

// 生成角色编码的计数器
let roleIdCounter = 6

// 表格数据
const tableData = ref([
  {
    id: 1,
    roleCode: 'ROLE001',
    roleName: '系统管理员',
    roleDescription: '系统超级管理员，拥有所有系统权限'
  },
  {
    id: 2,
    roleCode: 'ROLE002',
    roleName: '业务管理员',
    roleDescription: '业务管理员，负责业务相关功能管理'
  },
  {
    id: 3,
    roleCode: 'ROLE003',
    roleName: '风控专员',
    roleDescription: '负责风险控制相关工作'
  },
  {
    id: 4,
    roleCode: 'ROLE004',
    roleName: '普通用户',
    roleDescription: '普通用户，只能查看和操作基本功能'
  },
  {
    id: 5,
    roleCode: 'ROLE005',
    roleName: '财务部',
    roleDescription: '负责财务相关业务处理'
  }
])

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const isEdit = ref(false)

// 菜单权限对话框
const menuPermissionDialogVisible = ref(false)
const menuTreeRef = ref()
const checkedMenus = ref<number[]>([])

// 表单数据
const formData = reactive({
  id: null,
  roleCode: '',
  roleName: '',
  roleDescription: ''
})

// 表单验证规则
const formRules = {
  roleName: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value && isEdit.value) {
          // 编辑时检查角色名称是否重复（排除自己）
          const exists = tableData.value.some(role =>
            role.roleName === value && role.id !== formData.id
          )
          if (exists) {
            callback(new Error('角色名称已存在'))
          } else {
            callback()
          }
        } else if (value && !isEdit.value) {
          // 新增时检查角色名称是否重复
          const exists = tableData.value.some(role => role.roleName === value)
          if (exists) {
            callback(new Error('角色名称已存在'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 菜单树数据
const menuTreeData = ref([
  {
    id: 1,
    menuName: '特殊保证金申请管理',
    menuDescription: '特殊保证金相关业务管理',
    children: [
      { id: 11, menuName: '特保模板管理', menuDescription: '管理特保模板' },
      { id: 12, menuName: '特保申请参数配置', menuDescription: '配置申请参数' },
      { id: 13, menuName: '模板化特保查询', menuDescription: '查询模板化特保' },
      { id: 14, menuName: '个性化特保查询', menuDescription: '查询个性化特保' },
      { id: 15, menuName: '特殊保证金率申请', menuDescription: '申请特殊保证金率' }
    ]
  },
  {
    id: 2,
    menuName: '业务办理中心',
    menuDescription: '业务流程办理中心',
    children: [
      { id: 21, menuName: '发起流程', menuDescription: '发起业务流程' },
      { id: 22, menuName: '流程明细--特殊保证金率申请', menuDescription: '查看流程明细' },
      { id: 23, menuName: '影像归档--OA特保流程', menuDescription: '影像归档管理' }
    ]
  },
  {
    id: 3,
    menuName: '系统设置',
    menuDescription: '系统管理设置',
    children: [
      { id: 31, menuName: '菜单管理', menuDescription: '管理系统菜单' },
      { id: 32, menuName: '角色管理', menuDescription: '管理用户角色' },
      { id: 33, menuName: '用户管理', menuDescription: '管理系统用户' }
    ]
  }
])

// 生成角色编码
const generateRoleCode = () => {
  const code = `ROLE${String(roleIdCounter).padStart(3, '0')}`
  roleIdCounter++
  return code
}

// 查询
const handleQuery = () => {
  ElMessage.success('查询成功')
}

// 重置
const resetQuery = () => {
  Object.assign(queryForm, {
    roleCode: '',
    roleName: ''
  })
}

// 导出
const handleExport = () => {
  ElMessage.success('导出成功')
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增角色'
  isEdit.value = false
  resetForm()
  formData.roleCode = generateRoleCode()
  dialogVisible.value = true
}

// 修改
const handleEdit = (row: any) => {
  dialogTitle.value = '修改角色'
  isEdit.value = true
  Object.assign(formData, {
    id: row.id,
    roleCode: row.roleCode,
    roleName: row.roleName,
    roleDescription: row.roleDescription
  })
  dialogVisible.value = true
}

// 菜单权限
const handleMenuPermission = (row: any) => {
  // 重置选中状态
  checkedMenus.value = []

  // 模拟获取角色已有菜单权限
  if (row.roleName === '系统管理员') {
    checkedMenus.value = [1, 11, 12, 13, 14, 15, 2, 21, 22, 23, 3, 31, 32, 33]
  } else if (row.roleName === '业务管理员') {
    checkedMenus.value = [1, 11, 12, 13, 14, 15, 2, 21, 22, 23]
  } else if (row.roleName === '风控专员') {
    checkedMenus.value = [1, 13, 14, 2, 22, 23]
  } else {
    checkedMenus.value = [1, 13, 14]
  }

  menuPermissionDialogVisible.value = true
}

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确认要删除角色[${row.roleName}]？`, '提示', {
    confirmButtonText: '是',
    cancelButtonText: '否',
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
  })
}

// 提交表单
const handleSubmit = () => {
  if (!formData.roleName) {
    ElMessage.warning('请输入角色名称')
    return
  }

  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('保存成功')
      dialogVisible.value = false
    }
  })
}

// 提交菜单权限
const handleMenuPermissionSubmit = () => {
  const checkedKeys = menuTreeRef.value?.getCheckedKeys()
  console.log('选中的菜单权限:', checkedKeys)
  ElMessage.success('菜单权限配置成功')
  menuPermissionDialogVisible.value = false
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: null,
    roleCode: '',
    roleName: '',
    roleDescription: ''
  })
}

// 关闭对话框
const handleDialogClose = () => {
  resetForm()
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.role-management {
  padding: 20px;
}

.query-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.query-form {
  margin: 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.menu-tree-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.menu-name {
  font-weight: 500;
  margin-right: 10px;
  min-width: 150px;
}

.menu-description {
  color: #909399;
  font-size: 12px;
}

.menu-style {
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #f9f9f9;
  padding: 10px 0;
}
</style>
