<template>
  <div class="research-container">
    <!-- 筛选工具栏 -->
    <div class="filter-bar">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleDateChange"
          />
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="selectedCategory"
            placeholder="报告类型"
            clearable
            @change="handleFilterChange"
          >
            <el-option
              v-for="item in categories"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="selectedStatus"
            placeholder="状态筛选"
            clearable
            @change="handleFilterChange"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索报告标题/内容"
            clearable
            @input="handleSearch"
          >
            <template #append>
              <el-button :icon="Search" />
            </template>
          </el-input>
        </el-col>
      </el-row>
    </div>

    <!-- 报告列表 -->
    <div class="report-list">
        <template v-if="reportList.length > 0">
            <!-- 每行显示 4 个卡片 -->
            <el-row :gutter="20">
            <el-col
                v-for="report in reportList"
                :key="report.id"
                :xs="24"
                :sm="12"
                :md="8"
                :lg="6"
            >
                <ResearchReportItem
                :report="report"
                @click="handleCardClick(report)"
                @download="handleDownload"
                @favorite="handleFavorite"
                />
            </el-col>
            </el-row>
        </template>

        <el-empty v-else description="暂无报告数据" />
    </div>

    <!-- 分页器 -->
    <div class="pagination">
        <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="totalReports"
            :page-sizes="[10, 20, 50, 100]"
            layout="prev, pager, next, jumper, total"
            :pager-count="5"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import ResearchReportItem from '~/components/research/ResearchReportItem.vue'
// import { formatDate } from '@/utils/date'
// import { fetchReports } from '@/api/research'

// 筛选条件
const dateRange = ref([])
const selectedCategory = ref('')
const selectedStatus = ref('')
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const totalReports = ref(0)

// 模拟数据选项
const categories = [
  { value: 'macro', label: '宏观报告' },
  { value: 'commodity', label: '商品期货' },
  { value: 'financial', label: '金融期货' },
  { value: 'strategy', label: '投资策略' }
]

const statusOptions = [
  { value: 'draft', label: '草稿' },
  { value: 'published', label: '已发布' },
  { value: 'archived', label: '已归档' }
]

// 报告数据
const reportList = ref([])

// 按日期分组的报告数据
// const groupedReports = computed(() => {
//   const groups = {}
  
//   reportList.value.forEach(report => {
//     const dateKey = report.publishDate.split(' ')[0] // 按日期分组，忽略时间部分
    
//     if (!groups[dateKey]) {
//       groups[dateKey] = {
//         date: dateKey,
//         reports: []
//       }
//     }
    
//     groups[dateKey].reports.push(report)
//   })
  
//   // 按日期倒序排列
//   return Object.values(groups).sort((a, b) => new Date(b.date) - new Date(a.date))
// })

// 测试数据
// 测试数据
const mockReports = [
  {
    id: 1,
    title: '2024年第一季度宏观经济分析',
    category: 'macro',
    summary: '本报告对2024年第一季度国内宏观经济运行情况进行全面分析...',
    publishDate: '2024-04-05',
    author: '张三',
    status: 'published',
    downloadCount: 120,
    favoriteCount: 35
  },
  {
    id: 2,
    title: '铜期货市场走势预测报告',
    category: 'commodity',
    summary: '通过对供需关系和技术面的综合分析，预测未来三个月铜价走势...',
    publishDate: '2024-04-04',
    author: '李四',
    status: 'published',
    downloadCount: 89,
    favoriteCount: 27
  },
  {
    id: 3,
    title: '股指期货投资策略建议',
    category: 'financial',
    summary: '基于当前经济形势与政策变化，提出二季度股指期货操作建议...',
    publishDate: '2024-04-03',
    author: '王五',
    status: 'draft',
    downloadCount: 0,
    favoriteCount: 10
  },
  {
    id: 4,
    title: '农产品期货套利机会分析',
    category: 'commodity',
    summary: '分析玉米、大豆等主要农产品之间的跨品种套利机会及风险控制...',
    publishDate: '2024-04-02',
    author: '赵六',
    status: 'published',
    downloadCount: 67,
    favoriteCount: 18
  },
  {
    id: 5,
    title: '2024年中国新能源产业发展趋势展望',
    category: 'macro',
    summary: '本报告总结了中国新能源产业在2024年的最新发展趋势及政策影响...',
    publishDate: '2024-04-01',
    author: '钱七',
    status: 'published',
    downloadCount: 95,
    favoriteCount: 22
  },
  {
    id: 6,
    title: '原油价格波动对全球经济的影响研究',
    category: 'commodity',
    summary: '探讨近期国际原油价格波动对全球主要经济体的影响机制...',
    publishDate: '2024-03-31',
    author: '孙八',
    status: 'published',
    downloadCount: 110,
    favoriteCount: 29
  },
  {
    id: 7,
    title: '人工智能在金融领域的应用前景',
    category: 'financial',
    summary: '分析AI技术在银行、保险、证券等金融细分领域的落地情况及前景...',
    publishDate: '2024-03-30',
    author: '周九',
    status: 'draft',
    downloadCount: 0,
    favoriteCount: 5
  },
  {
    id: 8,
    title: '碳中和目标下的钢铁行业转型分析',
    category: 'macro',
    summary: '研究碳中和政策背景下，钢铁行业的绿色转型路径与挑战...',
    publishDate: '2024-03-29',
    author: '吴十',
    status: 'published',
    downloadCount: 88,
    favoriteCount: 17
  },
  {
    id: 9,
    title: '黄金期货市场短期波动趋势分析',
    category: 'commodity',
    summary: '结合地缘政治与美元走势，分析黄金市场的短期波动趋势...',
    publishDate: '2024-03-28',
    author: '郑十一',
    status: 'published',
    downloadCount: 73,
    favoriteCount: 12
  },
  {
    id: 10,
    title: '期权交易策略与风险管理实践',
    category: 'financial',
    summary: '介绍期权交易中的常见策略及其在实际投资中的风险管理应用...',
    publishDate: '2024-03-27',
    author: '王十二',
    status: 'archived',
    downloadCount: 56,
    favoriteCount: 9
  },
  {
    id: 11,
    title: '2024年A股市场行情回顾与展望',
    category: 'financial',
    summary: '回顾2024年上半年A股市场走势，并对未来趋势进行预测...',
    publishDate: '2024-03-26',
    author: '刘十三',
    status: 'published',
    downloadCount: 91,
    favoriteCount: 20
  },
  {
    id: 12,
    title: '锂电池产业链发展现状与投资机会',
    category: 'commodity',
    summary: '梳理锂电池上下游产业链的发展现状，并挖掘其中的投资机会...',
    publishDate: '2024-03-25',
    author: '陈十四',
    status: 'published',
    downloadCount: 78,
    favoriteCount: 15
  },
  {
    id: 13,
    title: '区块链技术在供应链管理中的应用探索',
    category: 'strategy',
    summary: '分析区块链技术如何提升供应链透明度与效率，提出应用案例...',
    publishDate: '2024-03-24',
    author: '黄十五',
    status: 'draft',
    downloadCount: 0,
    favoriteCount: 8
  },
  {
    id: 14,
    title: '云计算服务市场增长趋势与竞争格局',
    category: 'strategy',
    summary: '分析2024年全球云计算服务市场的发展趋势与主要厂商竞争格局...',
    publishDate: '2024-03-23',
    author: '徐十六',
    status: 'published',
    downloadCount: 102,
    favoriteCount: 24
  },
  {
    id: 15,
    title: '美联储加息对中国资本市场的影响分析',
    category: 'macro',
    summary: '探讨美联储货币政策调整对中国股市、债市及汇率的影响机制...',
    publishDate: '2024-03-22',
    author: '林十七',
    status: 'published',
    downloadCount: 97,
    favoriteCount: 21
  }
]

// 获取报告数据
// 模拟加载报告数据
const loadReports = () => {
  const keyword = searchKeyword.value.toLowerCase()
  const category = selectedCategory.value
  const status = selectedStatus.value

  // 过滤数据
  let filtered = mockReports.filter(report => {
    // return (
    //   (!category || report.category === category) &&
    //   (!status || report.status === status) &&
    //   (!keyword || report.title.toLowerCase().includes(keyword) || report.summary.toLowerCase().includes(keyword))
    // )
  })

  // 分页处理
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value

//   reportList.value = filtered.slice(start, end)
//   totalReports.value = filtered.length

  reportList.value = mockReports
  totalReports.value = mockReports.length
  mockReports

  console.log('测试数据长度:', reportList.length)
}

// 处理筛选变化
const handleFilterChange = () => {
  currentPage.value = 1
  loadReports()
}

// 处理日期变化
const handleDateChange = () => {
  handleFilterChange()
}

// 处理搜索
const handleSearch = () => {
  handleFilterChange()
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  loadReports()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadReports()
}

// 卡片点击事件
const handleCardClick = (report) => {
  console.log('打开报告详情:', report)
  // 这里可以跳转到详情页或打开弹窗
}

// 下载报告
const handleDownload = (report) => {
  console.log('下载报告:', report)
  // 实现下载逻辑
}

// 收藏报告
const handleFavorite = (report) => {
  console.log('收藏报告:', report)
  // 实现收藏逻辑
}

onMounted(() => {
  loadReports()
})
</script>

<style scoped>
.research-container {
  padding: 20px;
  background-color: #f5f7fa;
}

.filter-bar {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.report-list {
  margin-bottom: 20px;
}

.group-date {
  margin: 20px 0;
}

.date-text {
  font-size: 16px;
  font-weight: bold;
  color: var(--el-color-primary);
}

.count-badge {
  margin-left: 8px;
  padding: 2px 6px;
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  border-radius: 10px;
  font-size: 12px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  font-size: 14px;
}

.pagination :deep(.el-pager li) {
  background-color: transparent !important;
  color: #606266;
}

.pagination :deep(.el-pager li.active) {
  color: var(--el-color-primary);
  font-weight: bold;
}
</style>
