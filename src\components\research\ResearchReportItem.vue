<template>
  <el-card class="report-card" :body-style="{ padding: '0px' }" shadow="hover">
    <div class="card-content" @click="$emit('click', report)">
      <!-- 封面图片 -->
      <div class="cover-image" :style="{ backgroundImage: `url(${report.coverUrl || defaultCover})` }">
        <div v-if="report.isNew" class="new-badge">NEW</div>
        <div class="category-tag">{{ getCategoryLabel(report.category) }}</div>
      </div>
      
     <!-- 报告内容 -->
    <div class="report-info">
        <!-- 标题 -->
        <h3 class="title">{{ report.title }}</h3>

        <!-- 元信息 -->
        <div class="meta-wrapper">
            <div class="author">
            <el-avatar
                :size="30"
                :src="report.author.avatar || defaultAvatar"
                class="author-avatar"
            />
            <span class="author-name">{{ report.author }}</span>
            </div>
            <div class="time">{{ report.publishDate }}</div>
        </div>

        <!-- 摘要 -->
        <p class="summary">{{ report.summary }}</p>

        <!-- 操作按钮（保持不变） -->
        <div class="card-actions">
            <el-button text :icon="Star" @click.stop="$emit('favorite', report)">
            {{ report.isFavorite ? '已收藏' : '收藏' }}
            </el-button>
            <el-button text :icon="Download" @click.stop="$emit('download', report)">
            下载
            </el-button>
            <el-button text :icon="View" @click.stop="$emit('click', report)">
            查看
            </el-button>
        </div>
     </div>
    </div>
  </el-card>
</template>

<script setup>
import { Star, Download, View } from '@element-plus/icons-vue'
import defaultCover from '~/assets/images/default-cover.png'
import defaultAvatar from '~/assets/images/default-avatar.jpg'
// const defaultCover = new URL('/assets/images/default-cover.png', import.meta.url).href
// import { formatTime } from '@/utils/date'

const props = defineProps({
  report: {
    type: Object,
    required: true
  }
//   defaultCover: {
//     type: String,
//     default: () => '/h5/assets/images/default-cover.png'
//   }
})

// const defaultCover = require('@/assets/images/default-cover.png')


const getCategoryLabel = (value) => {
  const category = categories.find(item => item.value === value)
  return category ? category.label : value
}

// 从父组件导入的categories（实际项目中可以通过props或Vuex/Pinia传递）
const categories = [
  { value: 'macro', label: '宏观' },
  { value: 'commodity', label: '商品' },
  { value: 'financial', label: '金融' },
  { value: 'strategy', label: '策略' }
]
</script>

<style scoped >
.report-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s;
}

.report-card:hover {
  transform: translateY(-5px);
}

.card-content {
  cursor: pointer;
}

.cover-image {
  height: 150px;
  background-size: cover;
  background-position: center;
  position: relative;
}

.new-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--el-color-danger);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: bold;
}

.category-tag {
  position: absolute;
  top: 10px;
  left: 10px;
  /* background-color: var(--el-color-primary); */
  background-color: rgba(15, 25, 214, 0.85);
  color: rgb(255, 255, 255);
  padding: 4px 10px;
  border-radius: 6px;
  font-size: 18px;
  font-weight: bold;
  text-transform: uppercase;
}

.report-info {
  padding: 15px;
}

.title {
  margin: 0 0 8px 0;
  font-size: 18px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.meta-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.author {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.author .author-avatar {
  margin-right: 8px;
}

.author-name {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.time {
  font-size: 14px;
  color: var(--el-text-color-placeholder);
}

.summary {
  margin: 0 0 15px 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-actions {
  display: flex;
  justify-content: space-around;
  padding: 10px 0;
  border-top: 1px solid var(--el-border-color-light);
}

.cover-image {
  height: 150px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}
</style>
