<template>
  <el-menu mode="horizontal">
    <el-sub-menu v-for="(menu, index) in menus" :key="index" :index="menu.name">
      <template #title>{{ menu.name }}</template>
      <el-menu-item v-for="(item, i) in menu.children" :key="i" :index="item.route">{{ item.name }}</el-menu-item>
    </el-sub-menu>
  </el-menu>
</template>

<script setup>
import { defineProps } from 'vue';

const props = defineProps({
  menus: {
    type: Array,
    default: () => [],
  },
});
</script>

<style scoped>
/* 可以自定义 SubHeader 样式 */
</style>
