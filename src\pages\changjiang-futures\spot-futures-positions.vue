<template>
  <div class="spot-futures-positions">
    <h2>期现业务持仓表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="filterForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="开始日期">
              <el-date-picker
                v-model="filterForm.startDate"
                type="date"
                placeholder="选择开始日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束日期">
              <el-date-picker
                v-model="filterForm.endDate"
                type="date"
                placeholder="选择结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目（子账户）">
              <el-input v-model="filterForm.project" placeholder="请输入项目（子账户）" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="类型">
              <el-input v-model="filterForm.type" placeholder="请输入类型" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="品种">
              <el-input v-model="filterForm.variety" placeholder="请输入品种" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="合约代码">
              <el-input v-model="filterForm.contractCode" placeholder="请输入合约代码" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="paginatedData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="date" label="日期" width="100" align="center" />
        <el-table-column prop="project" label="项目（子账户）" width="120" align="center" />
        <el-table-column prop="type" label="类型" width="80" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.type === '期货' ? 'primary' : 'success'">
              {{ scope.row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="varietyCode" label="品种代码" width="100" align="center" />
        <el-table-column prop="varietyName" label="品种名称" width="120" align="center" />
        <el-table-column prop="contractCode" label="合约代码" width="120" align="center" />
        <el-table-column prop="contractName" label="合约名称" width="150" align="center" />
        <el-table-column prop="longPosition" label="多头" width="80" align="right" />
        <el-table-column prop="longNotionalPrincipal" label="多头持仓名义本金" width="150" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.longNotionalPrincipal) }}
          </template>
        </el-table-column>
        <el-table-column prop="shortPosition" label="空头" width="80" align="right" />
        <el-table-column prop="shortNotionalPrincipal" label="空头持仓名义本金" width="150" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.shortNotionalPrincipal) }}
          </template>
        </el-table-column>
        <el-table-column prop="netPosition" label="净头寸" width="80" align="right">
          <template #default="scope">
            <span :class="getPositionClass(scope.row.netPosition)">
              {{ scope.row.netPosition }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="netNotionalPrincipal" label="净头寸持仓名义本金" width="160" align="right">
          <template #default="scope">
            <span :class="getPositionClass(scope.row.netPosition)">
              {{ formatNumber(scope.row.netNotionalPrincipal) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="notionalPrincipal" label="名义本金" width="120" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.notionalPrincipal) }}
          </template>
        </el-table-column>
        <el-table-column prop="deltaExposure" label="敞口delta" width="120" align="right">
          <template #default="scope">
            <span :class="getExposureClass(scope.row.deltaExposure)">
              {{ formatNumber(scope.row.deltaExposure) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="gammaExposure" label="敞口gamma" width="120" align="right">
          <template #default="scope">
            <span :class="getExposureClass(scope.row.gammaExposure)">
              {{ scope.row.gammaExposure }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>



<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

const loading = ref(false)

const filterForm = reactive({
  startDate: '',
  endDate: '',
  project: '',
  type: '',
  variety: '',
  contractCode: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 20
})

// 生成20条样例数据
const spotFuturesData = ref([
  {
    date: '2025-06-18',
    project: '100050',
    type: '期货',
    varietyCode: 'l',
    varietyName: '聚乙烯',
    contractCode: 'l2507',
    contractName: '聚乙烯2507',
    longPosition: 0,
    longNotionalPrincipal: 0,
    shortPosition: 280,
    shortNotionalPrincipal: 10344600,
    netPosition: -280,
    netNotionalPrincipal: -10344600,
    notionalPrincipal: 10344600,
    deltaExposure: -10368400,
    gammaExposure: 0
  },
  {
    date: '2025-06-18',
    project: '100050',
    type: '期货',
    varietyCode: 'l',
    varietyName: '聚乙烯',
    contractCode: 'l2508',
    contractName: '聚乙烯2508',
    longPosition: 0,
    longNotionalPrincipal: 0,
    shortPosition: 63,
    shortNotionalPrincipal: 2331315,
    netPosition: -63,
    netNotionalPrincipal: -2331315,
    notionalPrincipal: 2331315,
    deltaExposure: -2336985,
    gammaExposure: 0
  },
  {
    date: '2025-06-18',
    project: '100050',
    type: '现货',
    varietyCode: 'l',
    varietyName: '聚乙烯',
    contractCode: '0',
    contractName: '0',
    longPosition: 1970,
    longNotionalPrincipal: 14111256,
    shortPosition: 0,
    shortNotionalPrincipal: 0,
    netPosition: 1970,
    netNotionalPrincipal: 14111256,
    notionalPrincipal: 14111256,
    deltaExposure: 14111256,
    gammaExposure: 0
  },
  {
    date: '2025-06-17',
    project: '100051',
    type: '期货',
    varietyCode: 'pp',
    varietyName: '聚丙烯',
    contractCode: 'pp2507',
    contractName: '聚丙烯2507',
    longPosition: 150,
    longNotionalPrincipal: 12450000,
    shortPosition: 0,
    shortNotionalPrincipal: 0,
    netPosition: 150,
    netNotionalPrincipal: 12450000,
    notionalPrincipal: 12450000,
    deltaExposure: 12450000,
    gammaExposure: 0
  },
  {
    date: '2025-06-17',
    project: '100051',
    type: '现货',
    varietyCode: 'pp',
    varietyName: '聚丙烯',
    contractCode: '0',
    contractName: '0',
    longPosition: 0,
    longNotionalPrincipal: 0,
    shortPosition: 800,
    shortNotionalPrincipal: 6640000,
    netPosition: -800,
    netNotionalPrincipal: -6640000,
    notionalPrincipal: 6640000,
    deltaExposure: -6640000,
    gammaExposure: 0
  },
  {
    date: '2025-06-16',
    project: '100052',
    type: '期货',
    varietyCode: 'rb',
    varietyName: '螺纹钢',
    contractCode: 'rb2510',
    contractName: '螺纹钢2510',
    longPosition: 200,
    longNotionalPrincipal: 8200000,
    shortPosition: 200,
    shortNotionalPrincipal: 8200000,
    netPosition: 0,
    netNotionalPrincipal: 0,
    notionalPrincipal: 8200000,
    deltaExposure: 0,
    gammaExposure: 0
  },
  {
    date: '2025-06-16',
    project: '100052',
    type: '现货',
    varietyCode: 'rb',
    varietyName: '螺纹钢',
    contractCode: '0',
    contractName: '0',
    longPosition: 1500,
    longNotionalPrincipal: 61500000,
    shortPosition: 0,
    shortNotionalPrincipal: 0,
    netPosition: 1500,
    netNotionalPrincipal: 61500000,
    notionalPrincipal: 61500000,
    deltaExposure: 61500000,
    gammaExposure: 0
  },
  {
    date: '2025-06-15',
    project: '100053',
    type: '期货',
    varietyCode: 'cu',
    varietyName: '沪铜',
    contractCode: 'cu2507',
    contractName: '沪铜2507',
    longPosition: 80,
    longNotionalPrincipal: 40000000,
    shortPosition: 50,
    shortNotionalPrincipal: 25000000,
    netPosition: 30,
    netNotionalPrincipal: 15000000,
    notionalPrincipal: 40000000,
    deltaExposure: 15000000,
    gammaExposure: 0
  },
  {
    date: '2025-06-15',
    project: '100053',
    type: '现货',
    varietyCode: 'cu',
    varietyName: '沪铜',
    contractCode: '0',
    contractName: '0',
    longPosition: 0,
    longNotionalPrincipal: 0,
    shortPosition: 300,
    shortNotionalPrincipal: 150000000,
    netPosition: -300,
    netNotionalPrincipal: -150000000,
    notionalPrincipal: 150000000,
    deltaExposure: -150000000,
    gammaExposure: 0
  },
  {
    date: '2025-06-14',
    project: '100054',
    type: '期货',
    varietyCode: 'al',
    varietyName: '沪铝',
    contractCode: 'al2508',
    contractName: '沪铝2508',
    longPosition: 120,
    longNotionalPrincipal: 22800000,
    shortPosition: 120,
    shortNotionalPrincipal: 22800000,
    netPosition: 0,
    netNotionalPrincipal: 0,
    notionalPrincipal: 22800000,
    deltaExposure: 0,
    gammaExposure: 0
  },
  {
    date: '2025-06-14',
    project: '100054',
    type: '现货',
    varietyCode: 'al',
    varietyName: '沪铝',
    contractCode: '0',
    contractName: '0',
    longPosition: 600,
    longNotionalPrincipal: 114000000,
    shortPosition: 0,
    shortNotionalPrincipal: 0,
    netPosition: 600,
    netNotionalPrincipal: 114000000,
    notionalPrincipal: 114000000,
    deltaExposure: 114000000,
    gammaExposure: 0
  },
  {
    date: '2025-06-13',
    project: '100055',
    type: '期货',
    varietyCode: 'zn',
    varietyName: '沪锌',
    contractCode: 'zn2509',
    contractName: '沪锌2509',
    longPosition: 0,
    longNotionalPrincipal: 0,
    shortPosition: 180,
    shortNotionalPrincipal: 45000000,
    netPosition: -180,
    netNotionalPrincipal: -45000000,
    notionalPrincipal: 45000000,
    deltaExposure: -45000000,
    gammaExposure: 0
  },
  {
    date: '2025-06-13',
    project: '100055',
    type: '现货',
    varietyCode: 'zn',
    varietyName: '沪锌',
    contractCode: '0',
    contractName: '0',
    longPosition: 900,
    longNotionalPrincipal: 225000000,
    shortPosition: 0,
    shortNotionalPrincipal: 0,
    netPosition: 900,
    netNotionalPrincipal: 225000000,
    notionalPrincipal: 225000000,
    deltaExposure: 225000000,
    gammaExposure: 0
  },
  {
    date: '2025-06-12',
    project: '100056',
    type: '期货',
    varietyCode: 'ni',
    varietyName: '沪镍',
    contractCode: 'ni2510',
    contractName: '沪镍2510',
    longPosition: 40,
    longNotionalPrincipal: 48000000,
    shortPosition: 40,
    shortNotionalPrincipal: 48000000,
    netPosition: 0,
    netNotionalPrincipal: 0,
    notionalPrincipal: 48000000,
    deltaExposure: 0,
    gammaExposure: 0
  },
  {
    date: '2025-06-12',
    project: '100056',
    type: '现货',
    varietyCode: 'ni',
    varietyName: '沪镍',
    contractCode: '0',
    contractName: '0',
    longPosition: 200,
    longNotionalPrincipal: 240000000,
    shortPosition: 0,
    shortNotionalPrincipal: 0,
    netPosition: 200,
    netNotionalPrincipal: 240000000,
    notionalPrincipal: 240000000,
    deltaExposure: 240000000,
    gammaExposure: 0
  },
  {
    date: '2025-06-11',
    project: '100057',
    type: '期货',
    varietyCode: 'ag',
    varietyName: '白银',
    contractCode: 'ag2512',
    contractName: '白银2512',
    longPosition: 100,
    longNotionalPrincipal: 75000000,
    shortPosition: 0,
    shortNotionalPrincipal: 0,
    netPosition: 100,
    netNotionalPrincipal: 75000000,
    notionalPrincipal: 75000000,
    deltaExposure: 75000000,
    gammaExposure: 0
  },
  {
    date: '2025-06-11',
    project: '100057',
    type: '现货',
    varietyCode: 'ag',
    varietyName: '白银',
    contractCode: '0',
    contractName: '0',
    longPosition: 0,
    longNotionalPrincipal: 0,
    shortPosition: 500,
    shortNotionalPrincipal: 375000000,
    netPosition: -500,
    netNotionalPrincipal: -375000000,
    notionalPrincipal: 375000000,
    deltaExposure: -375000000,
    gammaExposure: 0
  },
  {
    date: '2025-06-10',
    project: '100058',
    type: '期货',
    varietyCode: 'au',
    varietyName: '黄金',
    contractCode: 'au2512',
    contractName: '黄金2512',
    longPosition: 20,
    longNotionalPrincipal: 100000000,
    shortPosition: 20,
    shortNotionalPrincipal: 100000000,
    netPosition: 0,
    netNotionalPrincipal: 0,
    notionalPrincipal: 100000000,
    deltaExposure: 0,
    gammaExposure: 0
  },
  {
    date: '2025-06-10',
    project: '100058',
    type: '现货',
    varietyCode: 'au',
    varietyName: '黄金',
    contractCode: '0',
    contractName: '0',
    longPosition: 100,
    longNotionalPrincipal: 500000000,
    shortPosition: 0,
    shortNotionalPrincipal: 0,
    netPosition: 100,
    netNotionalPrincipal: 500000000,
    notionalPrincipal: 500000000,
    deltaExposure: 500000000,
    gammaExposure: 0
  },
  {
    date: '2025-06-09',
    project: '100059',
    type: '期货',
    varietyCode: 'i',
    varietyName: '铁矿石',
    contractCode: 'i2509',
    contractName: '铁矿石2509',
    longPosition: 0,
    longNotionalPrincipal: 0,
    shortPosition: 300,
    shortNotionalPrincipal: 24000000,
    netPosition: -300,
    netNotionalPrincipal: -24000000,
    notionalPrincipal: 24000000,
    deltaExposure: -24000000,
    gammaExposure: 0
  },
  {
    date: '2025-06-09',
    project: '100059',
    type: '现货',
    varietyCode: 'i',
    varietyName: '铁矿石',
    contractCode: '0',
    contractName: '0',
    longPosition: 1500,
    longNotionalPrincipal: 120000000,
    shortPosition: 0,
    shortNotionalPrincipal: 0,
    netPosition: 1500,
    netNotionalPrincipal: 120000000,
    notionalPrincipal: 120000000,
    deltaExposure: 120000000,
    gammaExposure: 0
  }
])

// 计算属性 - 过滤数据
const filteredData = computed(() => {
  let filtered = spotFuturesData.value

  // 按日期筛选
  if (filterForm.startDate) {
    filtered = filtered.filter(item => item.date >= filterForm.startDate)
  }
  if (filterForm.endDate) {
    filtered = filtered.filter(item => item.date <= filterForm.endDate)
  }

  // 按其他条件筛选
  if (filterForm.project) {
    filtered = filtered.filter(item => item.project.includes(filterForm.project))
  }
  if (filterForm.type) {
    filtered = filtered.filter(item => item.type.includes(filterForm.type))
  }
  if (filterForm.variety) {
    filtered = filtered.filter(item => item.varietyCode.includes(filterForm.variety) || item.varietyName.includes(filterForm.variety))
  }
  if (filterForm.contractCode) {
    filtered = filtered.filter(item => item.contractCode.includes(filterForm.contractCode))
  }

  return filtered
})

// 分页数据
const paginatedData = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredData.value.slice(start, end)
})

// 格式化数字（不带货币符号）
const formatNumber = (value: number) => {
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value)
}

// 获取持仓样式
const getPositionClass = (position: number) => {
  if (position > 0) return 'positive-position'
  if (position < 0) return 'negative-position'
  return 'zero-position'
}

// 获取敞口样式
const getExposureClass = (exposure: number) => {
  if (exposure > 0) return 'positive-exposure'
  if (exposure < 0) return 'negative-exposure'
  return 'zero-exposure'
}

const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询完成')
    // 更新分页总数
    pagination.total = filteredData.value.length
  }, 1000)
}

const handleReset = () => {
  Object.assign(filterForm, {
    startDate: '',
    endDate: '',
    project: '',
    type: '',
    variety: '',
    contractCode: ''
  })
  handleSearch()
}

const handleExport = () => {
  ElMessage.success('导出成功')
}

const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.currentPage = 1
}

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
}

</script>

<style scoped>
.spot-futures-positions {
  padding: 20px;
}

.query-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.query-form {
  margin: 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.positive-position {
  color: #67c23a;
  font-weight: 600;
}

.negative-position {
  color: #f56c6c;
  font-weight: 600;
}

.zero-position {
  color: #909399;
  font-weight: 600;
}

.positive-exposure {
  color: #67c23a;
  font-weight: 600;
}

.negative-exposure {
  color: #f56c6c;
  font-weight: 600;
}

.zero-exposure {
  color: #909399;
  font-weight: 600;
}
</style>