<template>
  <div class="spot-futures-positions-container">
    <!-- 左侧菜单 -->
    <ChangjiangFuturesSide />

    <!-- 主体内容区域 -->
    <div class="content">
      <el-card class="main-card">
        <template #header>
          <div class="card-header">
            <span>期现业务持仓表</span>
            <div class="header-actions">
              <el-button type="primary" size="small">导出Excel</el-button>
              <el-button type="success" size="small">刷新数据</el-button>
            </div>
          </div>
        </template>

        <!-- 筛选条件 -->
        <div class="filter-section">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-select v-model="filterForm.variety" placeholder="品种" clearable>
                <el-option label="螺纹钢" value="RB" />
                <el-option label="铜" value="CU" />
                <el-option label="铁矿石" value="I" />
                <el-option label="焦炭" value="J" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select v-model="filterForm.businessType" placeholder="业务类型" clearable>
                <el-option label="套期保值" value="hedge" />
                <el-option label="套利交易" value="arbitrage" />
                <el-option label="基差交易" value="basis" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-date-picker
                v-model="filterForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="default"
              />
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 汇总信息 -->
        <div class="summary-section">
          <el-row :gutter="20">
            <el-col :span="4">
              <el-statistic title="现货持仓(吨)" :value="summaryData.spotPosition" />
            </el-col>
            <el-col :span="4">
              <el-statistic title="期货持仓(手)" :value="summaryData.futuresPosition" />
            </el-col>
            <el-col :span="4">
              <el-statistic title="总市值(万元)" :value="summaryData.totalMarketValue" :precision="2" />
            </el-col>
            <el-col :span="4">
              <el-statistic title="基差收益(万元)" :value="summaryData.basisProfit" :precision="2" />
            </el-col>
            <el-col :span="4">
              <el-statistic title="套保比例(%)" :value="summaryData.hedgeRatio" :precision="1" />
            </el-col>
            <el-col :span="4">
              <el-statistic title="风险敞口(万元)" :value="summaryData.riskExposure" :precision="2" />
            </el-col>
          </el-row>
        </div>

        <!-- 数据表格 -->
        <el-table :data="positionData" style="width: 100%" v-loading="loading">
          <el-table-column prop="variety" label="品种" width="80" />
          <el-table-column prop="businessType" label="业务类型" width="100">
            <template #default="scope">
              <el-tag :type="getBusinessTypeTag(scope.row.businessType)">
                {{ getBusinessTypeName(scope.row.businessType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="spotPosition" label="现货持仓(吨)" width="120" />
          <el-table-column prop="spotPrice" label="现货价格" width="100" />
          <el-table-column prop="futuresContract" label="期货合约" width="100" />
          <el-table-column prop="futuresPosition" label="期货持仓(手)" width="120" />
          <el-table-column prop="futuresPrice" label="期货价格" width="100" />
          <el-table-column prop="basis" label="基差" width="80">
            <template #default="scope">
              <span :class="scope.row.basis >= 0 ? 'positive-basis' : 'negative-basis'">
                {{ scope.row.basis }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="hedgeRatio" label="套保比例(%)" width="100" />
          <el-table-column prop="profitLoss" label="盈亏(万元)" width="120">
            <template #default="scope">
              <span :class="scope.row.profitLoss >= 0 ? 'profit' : 'loss'">
                {{ scope.row.profitLoss }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="riskExposure" label="风险敞口(万元)" width="130" />
          <el-table-column prop="updateTime" label="更新时间" width="150" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import ChangjiangFuturesSide from '~/components/layouts/ChangjiangFuturesSide.vue'

const loading = ref(false)

const filterForm = reactive({
  variety: '',
  businessType: '',
  dateRange: []
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 60
})

const summaryData = reactive({
  spotPosition: 5000,
  futuresPosition: 500,
  totalMarketValue: 20000.00,
  basisProfit: 150.50,
  hedgeRatio: 85.6,
  riskExposure: 2890.00
})

const positionData = ref([
  {
    variety: 'RB',
    businessType: 'hedge',
    spotPosition: 1000,
    spotPrice: 4000,
    futuresContract: 'RB2405',
    futuresPosition: 100,
    futuresPrice: 4050,
    basis: -50,
    hedgeRatio: 90.0,
    profitLoss: 50.00,
    riskExposure: 400.00,
    updateTime: '2024-01-15 14:30:00'
  },
  {
    variety: 'CU',
    businessType: 'arbitrage',
    spotPosition: 500,
    spotPrice: 70000,
    futuresContract: 'CU2405',
    futuresPosition: 50,
    futuresPrice: 69500,
    basis: 500,
    hedgeRatio: 95.0,
    profitLoss: 125.00,
    riskExposure: 175.00,
    updateTime: '2024-01-15 14:25:00'
  },
  {
    variety: 'I',
    businessType: 'basis',
    spotPosition: 2000,
    spotPrice: 800,
    futuresContract: 'I2405',
    futuresPosition: 200,
    futuresPosition: 200,
    futuresPrice: 820,
    basis: -20,
    hedgeRatio: 80.0,
    profitLoss: -40.00,
    riskExposure: 320.00,
    updateTime: '2024-01-15 14:20:00'
  },
  {
    variety: 'J',
    businessType: 'hedge',
    spotPosition: 1500,
    spotPrice: 2500,
    futuresContract: 'J2405',
    futuresPosition: 150,
    futuresPrice: 2480,
    basis: 20,
    hedgeRatio: 88.0,
    profitLoss: 30.00,
    riskExposure: 450.00,
    updateTime: '2024-01-15 14:15:00'
  }
])

const getBusinessTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    hedge: 'success',
    arbitrage: 'warning',
    basis: 'info'
  }
  return tagMap[type] || ''
}

const getBusinessTypeName = (type: string) => {
  const nameMap: Record<string, string> = {
    hedge: '套期保值',
    arbitrage: '套利交易',
    basis: '基差交易'
  }
  return nameMap[type] || type
}

const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询完成')
  }, 1000)
}

const handleReset = () => {
  Object.assign(filterForm, {
    variety: '',
    businessType: '',
    dateRange: []
  })
}

const handleDetail = (row: any) => {
  ElMessage.info(`查看详情: ${row.variety} ${row.businessType}`)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  handleSearch()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  handleSearch()
}
</script>

<style scoped>
.spot-futures-positions-container {
  display: flex;
  height: 100vh;
}

.content {
  flex: 1;
  padding: 20px;
  background-color: #f5f5f5;
}

.main-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.summary-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.profit {
  color: #67c23a;
  font-weight: bold;
}

.loss {
  color: #f56c6c;
  font-weight: bold;
}

.positive-basis {
  color: #67c23a;
  font-weight: bold;
}

.negative-basis {
  color: #f56c6c;
  font-weight: bold;
}
</style>
